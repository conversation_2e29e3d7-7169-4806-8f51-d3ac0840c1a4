/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Enhanced Spiritual color palette inspired by Hindu traditions
        'bhagwa': {
          50: '#fff8f1',
          100: '#ffecdb',
          200: '#ffd6b7',
          300: '#ffb887',
          400: '#ff9933', // Primary bhagwa/saffron
          500: '#ff8800',
          600: '#e6660a',
          700: '#cc4400',
          800: '#b33300',
          900: '#992200',
        },
        'saffron': {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#ff9933', // Updated to bhagwa
          600: '#ff8800',
          700: '#e6660a',
          800: '#cc4400',
          900: '#b33300',
        },
        'maroon': {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#dc2626', // Deep maroon/red
          600: '#b91c1c',
          700: '#991b1b',
          800: '#7f1d1d',
          900: '#651e1e',
        },
        'temple-gold': {
          50: '#fdfce8',
          100: '#fcf9c3',
          200: '#f9f08a',
          300: '#f7e047',
          400: '#facc15', // Primary gold
          500: '#eab308',
          600: '#ca8a04',
          700: '#a16207',
          800: '#854d0e',
          900: '#713f12',
        },
        'spiritual-blue': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6', // Primary blue
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        'sacred-orange': {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#ff6b35', // Vibrant sacred orange
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        'lotus-pink': {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899', // Lotus pink
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        },
        'divine-purple': {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7', // Divine purple
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        'earth-brown': {
          50: '#fdf8f6',
          100: '#f2e8e5',
          200: '#eaddd7',
          300: '#e0cfc5',
          400: '#d2bab0',
          500: '#a67c52', // Earth brown
          600: '#8b5a3c',
          700: '#73462f',
          800: '#5c3317',
          900: '#4a2c0a',
        },
      },
      fontFamily: {
        sans: ['var(--font-inter)'],
        serif: ['var(--font-noto-sans-devanagari)', 'var(--font-noto-serif-devanagari)'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'temple-pattern': "url('/images/temple-pattern.svg')",
        'om-symbol': "url('/images/om-symbol.svg')",
        'mandala-pattern': "url('/images/mandala-pattern.svg')",
        'lotus-pattern': "url('/images/lotus-pattern.svg')",
        'sacred-geometry': "url('/images/sacred-geometry.svg')",
        'gradient-saffron': 'linear-gradient(135deg, #ff9933 0%, #ff8800 100%)',
        'gradient-bhagwa': 'linear-gradient(135deg, #ff9933 0%, #ff8800 100%)',
        'gradient-divine': 'linear-gradient(135deg, #a855f7 0%, #7c3aed 100%)',
        'gradient-temple': 'linear-gradient(135deg, #facc15 0%, #ca8a04 100%)',
        'gradient-spiritual': 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
      },
      animation: {
        'diya-flicker': 'flicker 2s infinite',
        'lotus-float': 'float 6s ease-in-out infinite',
        'om-pulse': 'omPulse 3s ease-in-out infinite',
        'mandala-rotate': 'mandalaRotate 20s linear infinite',
        'sacred-glow': 'sacredGlow 4s ease-in-out infinite',
        'divine-shimmer': 'divineShimmer 2s ease-in-out infinite',
        'temple-bells': 'templeBells 1.5s ease-in-out infinite',
      },
      keyframes: {
        flicker: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.7 },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        omPulse: {
          '0%, 100%': { transform: 'scale(1)', opacity: 0.8 },
          '50%': { transform: 'scale(1.05)', opacity: 1 },
        },
        mandalaRotate: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        sacredGlow: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(255, 153, 51, 0.3)' },
          '50%': { boxShadow: '0 0 40px rgba(255, 153, 51, 0.6)' },
        },
        divineShimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        templeBells: {
          '0%, 100%': { transform: 'rotate(-2deg)' },
          '50%': { transform: 'rotate(2deg)' },
        },
      },
    },
  },
  plugins: [],
}