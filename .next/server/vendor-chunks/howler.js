/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/howler";
exports.ids = ["vendor-chunks/howler"];
exports.modules = {

/***/ "(ssr)/./node_modules/howler/dist/howler.js":
/*!********************************************!*\
  !*** ./node_modules/howler/dist/howler.js ***!
  \********************************************/
/***/ ((module, exports) => {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*!\n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, James Simpson of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */ (function() {\n    \"use strict\";\n    /** Global Methods **/ /***************************************************************************/ /**\n   * Create the global controller. All contained methods and properties apply\n   * to all sounds that are currently playing or will be in the future.\n   */ var HowlerGlobal1 = function() {\n        this.init();\n    };\n    HowlerGlobal1.prototype = {\n        /**\n     * Initialize the global Howler object.\n     * @return {Howler}\n     */ init: function() {\n            var self = this || Howler1;\n            // Create a global ID counter.\n            self._counter = 1000;\n            // Pool of unlocked HTML5 Audio objects.\n            self._html5AudioPool = [];\n            self.html5PoolSize = 10;\n            // Internal properties.\n            self._codecs = {};\n            self._howls = [];\n            self._muted = false;\n            self._volume = 1;\n            self._canPlayEvent = \"canplaythrough\";\n            self._navigator =  false ? 0 : null;\n            // Public properties.\n            self.masterGain = null;\n            self.noAudio = false;\n            self.usingWebAudio = true;\n            self.autoSuspend = true;\n            self.ctx = null;\n            // Set to false to disable the auto audio unlocker.\n            self.autoUnlock = true;\n            // Setup the various state values for global tracking.\n            self._setup();\n            return self;\n        },\n        /**\n     * Get/set the global volume for all sounds.\n     * @param  {Float} vol Volume from 0.0 to 1.0.\n     * @return {Howler/Float}     Returns self or current volume.\n     */ volume: function(vol) {\n            var self = this || Howler1;\n            vol = parseFloat(vol);\n            // If we don't have an AudioContext created yet, run the setup.\n            if (!self.ctx) {\n                setupAudioContext();\n            }\n            if (typeof vol !== \"undefined\" && vol >= 0 && vol <= 1) {\n                self._volume = vol;\n                // Don't update any of the nodes if we are muted.\n                if (self._muted) {\n                    return self;\n                }\n                // When using Web Audio, we just need to adjust the master gain.\n                if (self.usingWebAudio) {\n                    self.masterGain.gain.setValueAtTime(vol, Howler1.ctx.currentTime);\n                }\n                // Loop through and change volume for all HTML5 audio nodes.\n                for(var i = 0; i < self._howls.length; i++){\n                    if (!self._howls[i]._webAudio) {\n                        // Get all of the sounds in this Howl group.\n                        var ids = self._howls[i]._getSoundIds();\n                        // Loop through all sounds and change the volumes.\n                        for(var j = 0; j < ids.length; j++){\n                            var sound = self._howls[i]._soundById(ids[j]);\n                            if (sound && sound._node) {\n                                sound._node.volume = sound._volume * vol;\n                            }\n                        }\n                    }\n                }\n                return self;\n            }\n            return self._volume;\n        },\n        /**\n     * Handle muting and unmuting globally.\n     * @param  {Boolean} muted Is muted or not.\n     */ mute: function(muted) {\n            var self = this || Howler1;\n            // If we don't have an AudioContext created yet, run the setup.\n            if (!self.ctx) {\n                setupAudioContext();\n            }\n            self._muted = muted;\n            // With Web Audio, we just need to mute the master gain.\n            if (self.usingWebAudio) {\n                self.masterGain.gain.setValueAtTime(muted ? 0 : self._volume, Howler1.ctx.currentTime);\n            }\n            // Loop through and mute all HTML5 Audio nodes.\n            for(var i = 0; i < self._howls.length; i++){\n                if (!self._howls[i]._webAudio) {\n                    // Get all of the sounds in this Howl group.\n                    var ids = self._howls[i]._getSoundIds();\n                    // Loop through all sounds and mark the audio node as muted.\n                    for(var j = 0; j < ids.length; j++){\n                        var sound = self._howls[i]._soundById(ids[j]);\n                        if (sound && sound._node) {\n                            sound._node.muted = muted ? true : sound._muted;\n                        }\n                    }\n                }\n            }\n            return self;\n        },\n        /**\n     * Handle stopping all sounds globally.\n     */ stop: function() {\n            var self = this || Howler1;\n            // Loop through all Howls and stop them.\n            for(var i = 0; i < self._howls.length; i++){\n                self._howls[i].stop();\n            }\n            return self;\n        },\n        /**\n     * Unload and destroy all currently loaded Howl objects.\n     * @return {Howler}\n     */ unload: function() {\n            var self = this || Howler1;\n            for(var i = self._howls.length - 1; i >= 0; i--){\n                self._howls[i].unload();\n            }\n            // Create a new AudioContext to make sure it is fully reset.\n            if (self.usingWebAudio && self.ctx && typeof self.ctx.close !== \"undefined\") {\n                self.ctx.close();\n                self.ctx = null;\n                setupAudioContext();\n            }\n            return self;\n        },\n        /**\n     * Check for codec support of specific extension.\n     * @param  {String} ext Audio file extention.\n     * @return {Boolean}\n     */ codecs: function(ext) {\n            return (this || Howler1)._codecs[ext.replace(/^x-/, \"\")];\n        },\n        /**\n     * Setup various state values for global tracking.\n     * @return {Howler}\n     */ _setup: function() {\n            var self = this || Howler1;\n            // Keeps track of the suspend/resume state of the AudioContext.\n            self.state = self.ctx ? self.ctx.state || \"suspended\" : \"suspended\";\n            // Automatically begin the 30-second suspend process\n            self._autoSuspend();\n            // Check if audio is available.\n            if (!self.usingWebAudio) {\n                // No audio is available on this system if noAudio is set to true.\n                if (typeof Audio !== \"undefined\") {\n                    try {\n                        var test = new Audio();\n                        // Check if the canplaythrough event is available.\n                        if (typeof test.oncanplaythrough === \"undefined\") {\n                            self._canPlayEvent = \"canplay\";\n                        }\n                    } catch (e) {\n                        self.noAudio = true;\n                    }\n                } else {\n                    self.noAudio = true;\n                }\n            }\n            // Test to make sure audio isn't disabled in Internet Explorer.\n            try {\n                var test = new Audio();\n                if (test.muted) {\n                    self.noAudio = true;\n                }\n            } catch (e) {}\n            // Check for supported codecs.\n            if (!self.noAudio) {\n                self._setupCodecs();\n            }\n            return self;\n        },\n        /**\n     * Check for browser support for various codecs and cache the results.\n     * @return {Howler}\n     */ _setupCodecs: function() {\n            var self = this || Howler1;\n            var audioTest = null;\n            // Must wrap in a try/catch because IE11 in server mode throws an error.\n            try {\n                audioTest = typeof Audio !== \"undefined\" ? new Audio() : null;\n            } catch (err) {\n                return self;\n            }\n            if (!audioTest || typeof audioTest.canPlayType !== \"function\") {\n                return self;\n            }\n            var mpegTest = audioTest.canPlayType(\"audio/mpeg;\").replace(/^no$/, \"\");\n            // Opera version <33 has mixed MP3 support, so we need to check for and block it.\n            var ua = self._navigator ? self._navigator.userAgent : \"\";\n            var checkOpera = ua.match(/OPR\\/(\\d+)/g);\n            var isOldOpera = checkOpera && parseInt(checkOpera[0].split(\"/\")[1], 10) < 33;\n            var checkSafari = ua.indexOf(\"Safari\") !== -1 && ua.indexOf(\"Chrome\") === -1;\n            var safariVersion = ua.match(/Version\\/(.*?) /);\n            var isOldSafari = checkSafari && safariVersion && parseInt(safariVersion[1], 10) < 15;\n            self._codecs = {\n                mp3: !!(!isOldOpera && (mpegTest || audioTest.canPlayType(\"audio/mp3;\").replace(/^no$/, \"\"))),\n                mpeg: !!mpegTest,\n                opus: !!audioTest.canPlayType('audio/ogg; codecs=\"opus\"').replace(/^no$/, \"\"),\n                ogg: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, \"\"),\n                oga: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, \"\"),\n                wav: !!(audioTest.canPlayType('audio/wav; codecs=\"1\"') || audioTest.canPlayType(\"audio/wav\")).replace(/^no$/, \"\"),\n                aac: !!audioTest.canPlayType(\"audio/aac;\").replace(/^no$/, \"\"),\n                caf: !!audioTest.canPlayType(\"audio/x-caf;\").replace(/^no$/, \"\"),\n                m4a: !!(audioTest.canPlayType(\"audio/x-m4a;\") || audioTest.canPlayType(\"audio/m4a;\") || audioTest.canPlayType(\"audio/aac;\")).replace(/^no$/, \"\"),\n                m4b: !!(audioTest.canPlayType(\"audio/x-m4b;\") || audioTest.canPlayType(\"audio/m4b;\") || audioTest.canPlayType(\"audio/aac;\")).replace(/^no$/, \"\"),\n                mp4: !!(audioTest.canPlayType(\"audio/x-mp4;\") || audioTest.canPlayType(\"audio/mp4;\") || audioTest.canPlayType(\"audio/aac;\")).replace(/^no$/, \"\"),\n                weba: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, \"\")),\n                webm: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, \"\")),\n                dolby: !!audioTest.canPlayType('audio/mp4; codecs=\"ec-3\"').replace(/^no$/, \"\"),\n                flac: !!(audioTest.canPlayType(\"audio/x-flac;\") || audioTest.canPlayType(\"audio/flac;\")).replace(/^no$/, \"\")\n            };\n            return self;\n        },\n        /**\n     * Some browsers/devices will only allow audio to be played after a user interaction.\n     * Attempt to automatically unlock audio on the first user interaction.\n     * Concept from: http://paulbakaus.com/tutorials/html5/web-audio-on-ios/\n     * @return {Howler}\n     */ _unlockAudio: function() {\n            var self = this || Howler1;\n            // Only run this if Web Audio is supported and it hasn't already been unlocked.\n            if (self._audioUnlocked || !self.ctx) {\n                return;\n            }\n            self._audioUnlocked = false;\n            self.autoUnlock = false;\n            // Some mobile devices/platforms have distortion issues when opening/closing tabs and/or web views.\n            // Bugs in the browser (especially Mobile Safari) can cause the sampleRate to change from 44100 to 48000.\n            // By calling Howler.unload(), we create a new AudioContext with the correct sampleRate.\n            if (!self._mobileUnloaded && self.ctx.sampleRate !== 44100) {\n                self._mobileUnloaded = true;\n                self.unload();\n            }\n            // Scratch buffer for enabling iOS to dispose of web audio buffers correctly, as per:\n            // http://stackoverflow.com/questions/24119684\n            self._scratchBuffer = self.ctx.createBuffer(1, 1, 22050);\n            // Call this method on touch start to create and play a buffer,\n            // then check if the audio actually played to determine if\n            // audio has now been unlocked on iOS, Android, etc.\n            var unlock = function(e) {\n                // Create a pool of unlocked HTML5 Audio objects that can\n                // be used for playing sounds without user interaction. HTML5\n                // Audio objects must be individually unlocked, as opposed\n                // to the WebAudio API which only needs a single activation.\n                // This must occur before WebAudio setup or the source.onended\n                // event will not fire.\n                while(self._html5AudioPool.length < self.html5PoolSize){\n                    try {\n                        var audioNode = new Audio();\n                        // Mark this Audio object as unlocked to ensure it can get returned\n                        // to the unlocked pool when released.\n                        audioNode._unlocked = true;\n                        // Add the audio node to the pool.\n                        self._releaseHtml5Audio(audioNode);\n                    } catch (e) {\n                        self.noAudio = true;\n                        break;\n                    }\n                }\n                // Loop through any assigned audio nodes and unlock them.\n                for(var i = 0; i < self._howls.length; i++){\n                    if (!self._howls[i]._webAudio) {\n                        // Get all of the sounds in this Howl group.\n                        var ids = self._howls[i]._getSoundIds();\n                        // Loop through all sounds and unlock the audio nodes.\n                        for(var j = 0; j < ids.length; j++){\n                            var sound = self._howls[i]._soundById(ids[j]);\n                            if (sound && sound._node && !sound._node._unlocked) {\n                                sound._node._unlocked = true;\n                                sound._node.load();\n                            }\n                        }\n                    }\n                }\n                // Fix Android can not play in suspend state.\n                self._autoResume();\n                // Create an empty buffer.\n                var source = self.ctx.createBufferSource();\n                source.buffer = self._scratchBuffer;\n                source.connect(self.ctx.destination);\n                // Play the empty buffer.\n                if (typeof source.start === \"undefined\") {\n                    source.noteOn(0);\n                } else {\n                    source.start(0);\n                }\n                // Calling resume() on a stack initiated by user gesture is what actually unlocks the audio on Android Chrome >= 55.\n                if (typeof self.ctx.resume === \"function\") {\n                    self.ctx.resume();\n                }\n                // Setup a timeout to check that we are unlocked on the next event loop.\n                source.onended = function() {\n                    source.disconnect(0);\n                    // Update the unlocked state and prevent this check from happening again.\n                    self._audioUnlocked = true;\n                    // Remove the touch start listener.\n                    document.removeEventListener(\"touchstart\", unlock, true);\n                    document.removeEventListener(\"touchend\", unlock, true);\n                    document.removeEventListener(\"click\", unlock, true);\n                    document.removeEventListener(\"keydown\", unlock, true);\n                    // Let all sounds know that audio has been unlocked.\n                    for(var i = 0; i < self._howls.length; i++){\n                        self._howls[i]._emit(\"unlock\");\n                    }\n                };\n            };\n            // Setup a touch start listener to attempt an unlock in.\n            document.addEventListener(\"touchstart\", unlock, true);\n            document.addEventListener(\"touchend\", unlock, true);\n            document.addEventListener(\"click\", unlock, true);\n            document.addEventListener(\"keydown\", unlock, true);\n            return self;\n        },\n        /**\n     * Get an unlocked HTML5 Audio object from the pool. If none are left,\n     * return a new Audio object and throw a warning.\n     * @return {Audio} HTML5 Audio object.\n     */ _obtainHtml5Audio: function() {\n            var self = this || Howler1;\n            // Return the next object from the pool if one exists.\n            if (self._html5AudioPool.length) {\n                return self._html5AudioPool.pop();\n            }\n            //.Check if the audio is locked and throw a warning.\n            var testPlay = new Audio().play();\n            if (testPlay && typeof Promise !== \"undefined\" && (testPlay instanceof Promise || typeof testPlay.then === \"function\")) {\n                testPlay.catch(function() {\n                    console.warn(\"HTML5 Audio pool exhausted, returning potentially locked audio object.\");\n                });\n            }\n            return new Audio();\n        },\n        /**\n     * Return an activated HTML5 Audio object to the pool.\n     * @return {Howler}\n     */ _releaseHtml5Audio: function(audio) {\n            var self = this || Howler1;\n            // Don't add audio to the pool if we don't know if it has been unlocked.\n            if (audio._unlocked) {\n                self._html5AudioPool.push(audio);\n            }\n            return self;\n        },\n        /**\n     * Automatically suspend the Web Audio AudioContext after no sound has played for 30 seconds.\n     * This saves processing/energy and fixes various browser-specific bugs with audio getting stuck.\n     * @return {Howler}\n     */ _autoSuspend: function() {\n            var self = this;\n            if (!self.autoSuspend || !self.ctx || typeof self.ctx.suspend === \"undefined\" || !Howler1.usingWebAudio) {\n                return;\n            }\n            // Check if any sounds are playing.\n            for(var i = 0; i < self._howls.length; i++){\n                if (self._howls[i]._webAudio) {\n                    for(var j = 0; j < self._howls[i]._sounds.length; j++){\n                        if (!self._howls[i]._sounds[j]._paused) {\n                            return self;\n                        }\n                    }\n                }\n            }\n            if (self._suspendTimer) {\n                clearTimeout(self._suspendTimer);\n            }\n            // If no sound has played after 30 seconds, suspend the context.\n            self._suspendTimer = setTimeout(function() {\n                if (!self.autoSuspend) {\n                    return;\n                }\n                self._suspendTimer = null;\n                self.state = \"suspending\";\n                // Handle updating the state of the audio context after suspending.\n                var handleSuspension = function() {\n                    self.state = \"suspended\";\n                    if (self._resumeAfterSuspend) {\n                        delete self._resumeAfterSuspend;\n                        self._autoResume();\n                    }\n                };\n                // Either the state gets suspended or it is interrupted.\n                // Either way, we need to update the state to suspended.\n                self.ctx.suspend().then(handleSuspension, handleSuspension);\n            }, 30000);\n            return self;\n        },\n        /**\n     * Automatically resume the Web Audio AudioContext when a new sound is played.\n     * @return {Howler}\n     */ _autoResume: function() {\n            var self = this;\n            if (!self.ctx || typeof self.ctx.resume === \"undefined\" || !Howler1.usingWebAudio) {\n                return;\n            }\n            if (self.state === \"running\" && self.ctx.state !== \"interrupted\" && self._suspendTimer) {\n                clearTimeout(self._suspendTimer);\n                self._suspendTimer = null;\n            } else if (self.state === \"suspended\" || self.state === \"running\" && self.ctx.state === \"interrupted\") {\n                self.ctx.resume().then(function() {\n                    self.state = \"running\";\n                    // Emit to all Howls that the audio has resumed.\n                    for(var i = 0; i < self._howls.length; i++){\n                        self._howls[i]._emit(\"resume\");\n                    }\n                });\n                if (self._suspendTimer) {\n                    clearTimeout(self._suspendTimer);\n                    self._suspendTimer = null;\n                }\n            } else if (self.state === \"suspending\") {\n                self._resumeAfterSuspend = true;\n            }\n            return self;\n        }\n    };\n    // Setup the global audio controller.\n    var Howler1 = new HowlerGlobal1();\n    /** Group Methods **/ /***************************************************************************/ /**\n   * Create an audio group controller.\n   * @param {Object} o Passed in properties for this group.\n   */ var Howl1 = function(o) {\n        var self = this;\n        // Throw an error if no source is provided.\n        if (!o.src || o.src.length === 0) {\n            console.error(\"An array of source files must be passed with any new Howl.\");\n            return;\n        }\n        self.init(o);\n    };\n    Howl1.prototype = {\n        /**\n     * Initialize a new Howl group object.\n     * @param  {Object} o Passed in properties for this group.\n     * @return {Howl}\n     */ init: function(o) {\n            var self = this;\n            // If we don't have an AudioContext created yet, run the setup.\n            if (!Howler1.ctx) {\n                setupAudioContext();\n            }\n            // Setup user-defined default properties.\n            self._autoplay = o.autoplay || false;\n            self._format = typeof o.format !== \"string\" ? o.format : [\n                o.format\n            ];\n            self._html5 = o.html5 || false;\n            self._muted = o.mute || false;\n            self._loop = o.loop || false;\n            self._pool = o.pool || 5;\n            self._preload = typeof o.preload === \"boolean\" || o.preload === \"metadata\" ? o.preload : true;\n            self._rate = o.rate || 1;\n            self._sprite = o.sprite || {};\n            self._src = typeof o.src !== \"string\" ? o.src : [\n                o.src\n            ];\n            self._volume = o.volume !== undefined ? o.volume : 1;\n            self._xhr = {\n                method: o.xhr && o.xhr.method ? o.xhr.method : \"GET\",\n                headers: o.xhr && o.xhr.headers ? o.xhr.headers : null,\n                withCredentials: o.xhr && o.xhr.withCredentials ? o.xhr.withCredentials : false\n            };\n            // Setup all other default properties.\n            self._duration = 0;\n            self._state = \"unloaded\";\n            self._sounds = [];\n            self._endTimers = {};\n            self._queue = [];\n            self._playLock = false;\n            // Setup event listeners.\n            self._onend = o.onend ? [\n                {\n                    fn: o.onend\n                }\n            ] : [];\n            self._onfade = o.onfade ? [\n                {\n                    fn: o.onfade\n                }\n            ] : [];\n            self._onload = o.onload ? [\n                {\n                    fn: o.onload\n                }\n            ] : [];\n            self._onloaderror = o.onloaderror ? [\n                {\n                    fn: o.onloaderror\n                }\n            ] : [];\n            self._onplayerror = o.onplayerror ? [\n                {\n                    fn: o.onplayerror\n                }\n            ] : [];\n            self._onpause = o.onpause ? [\n                {\n                    fn: o.onpause\n                }\n            ] : [];\n            self._onplay = o.onplay ? [\n                {\n                    fn: o.onplay\n                }\n            ] : [];\n            self._onstop = o.onstop ? [\n                {\n                    fn: o.onstop\n                }\n            ] : [];\n            self._onmute = o.onmute ? [\n                {\n                    fn: o.onmute\n                }\n            ] : [];\n            self._onvolume = o.onvolume ? [\n                {\n                    fn: o.onvolume\n                }\n            ] : [];\n            self._onrate = o.onrate ? [\n                {\n                    fn: o.onrate\n                }\n            ] : [];\n            self._onseek = o.onseek ? [\n                {\n                    fn: o.onseek\n                }\n            ] : [];\n            self._onunlock = o.onunlock ? [\n                {\n                    fn: o.onunlock\n                }\n            ] : [];\n            self._onresume = [];\n            // Web Audio or HTML5 Audio?\n            self._webAudio = Howler1.usingWebAudio && !self._html5;\n            // Automatically try to enable audio.\n            if (typeof Howler1.ctx !== \"undefined\" && Howler1.ctx && Howler1.autoUnlock) {\n                Howler1._unlockAudio();\n            }\n            // Keep track of this Howl group in the global controller.\n            Howler1._howls.push(self);\n            // If they selected autoplay, add a play event to the load queue.\n            if (self._autoplay) {\n                self._queue.push({\n                    event: \"play\",\n                    action: function() {\n                        self.play();\n                    }\n                });\n            }\n            // Load the source file unless otherwise specified.\n            if (self._preload && self._preload !== \"none\") {\n                self.load();\n            }\n            return self;\n        },\n        /**\n     * Load the audio file.\n     * @return {Howler}\n     */ load: function() {\n            var self = this;\n            var url = null;\n            // If no audio is available, quit immediately.\n            if (Howler1.noAudio) {\n                self._emit(\"loaderror\", null, \"No audio support.\");\n                return;\n            }\n            // Make sure our source is in an array.\n            if (typeof self._src === \"string\") {\n                self._src = [\n                    self._src\n                ];\n            }\n            // Loop through the sources and pick the first one that is compatible.\n            for(var i = 0; i < self._src.length; i++){\n                var ext, str;\n                if (self._format && self._format[i]) {\n                    // If an extension was specified, use that instead.\n                    ext = self._format[i];\n                } else {\n                    // Make sure the source is a string.\n                    str = self._src[i];\n                    if (typeof str !== \"string\") {\n                        self._emit(\"loaderror\", null, \"Non-string found in selected audio sources - ignoring.\");\n                        continue;\n                    }\n                    // Extract the file extension from the URL or base64 data URI.\n                    ext = /^data:audio\\/([^;,]+);/i.exec(str);\n                    if (!ext) {\n                        ext = /\\.([^.]+)$/.exec(str.split(\"?\", 1)[0]);\n                    }\n                    if (ext) {\n                        ext = ext[1].toLowerCase();\n                    }\n                }\n                // Log a warning if no extension was found.\n                if (!ext) {\n                    console.warn('No file extension was found. Consider using the \"format\" property or specify an extension.');\n                }\n                // Check if this extension is available.\n                if (ext && Howler1.codecs(ext)) {\n                    url = self._src[i];\n                    break;\n                }\n            }\n            if (!url) {\n                self._emit(\"loaderror\", null, \"No codec support for selected audio sources.\");\n                return;\n            }\n            self._src = url;\n            self._state = \"loading\";\n            // If the hosting page is HTTPS and the source isn't,\n            // drop down to HTML5 Audio to avoid Mixed Content errors.\n            if (window.location.protocol === \"https:\" && url.slice(0, 5) === \"http:\") {\n                self._html5 = true;\n                self._webAudio = false;\n            }\n            // Create a new sound object and add it to the pool.\n            new Sound1(self);\n            // Load and decode the audio data for playback.\n            if (self._webAudio) {\n                loadBuffer(self);\n            }\n            return self;\n        },\n        /**\n     * Play a sound or resume previous playback.\n     * @param  {String/Number} sprite   Sprite name for sprite playback or sound id to continue previous.\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Number}          Sound ID.\n     */ play: function(sprite, internal) {\n            var self = this;\n            var id = null;\n            // Determine if a sprite, sound id or nothing was passed\n            if (typeof sprite === \"number\") {\n                id = sprite;\n                sprite = null;\n            } else if (typeof sprite === \"string\" && self._state === \"loaded\" && !self._sprite[sprite]) {\n                // If the passed sprite doesn't exist, do nothing.\n                return null;\n            } else if (typeof sprite === \"undefined\") {\n                // Use the default sound sprite (plays the full audio length).\n                sprite = \"__default\";\n                // Check if there is a single paused sound that isn't ended.\n                // If there is, play that sound. If not, continue as usual.\n                if (!self._playLock) {\n                    var num = 0;\n                    for(var i = 0; i < self._sounds.length; i++){\n                        if (self._sounds[i]._paused && !self._sounds[i]._ended) {\n                            num++;\n                            id = self._sounds[i]._id;\n                        }\n                    }\n                    if (num === 1) {\n                        sprite = null;\n                    } else {\n                        id = null;\n                    }\n                }\n            }\n            // Get the selected node, or get one from the pool.\n            var sound = id ? self._soundById(id) : self._inactiveSound();\n            // If the sound doesn't exist, do nothing.\n            if (!sound) {\n                return null;\n            }\n            // Select the sprite definition.\n            if (id && !sprite) {\n                sprite = sound._sprite || \"__default\";\n            }\n            // If the sound hasn't loaded, we must wait to get the audio's duration.\n            // We also need to wait to make sure we don't run into race conditions with\n            // the order of function calls.\n            if (self._state !== \"loaded\") {\n                // Set the sprite value on this sound.\n                sound._sprite = sprite;\n                // Mark this sound as not ended in case another sound is played before this one loads.\n                sound._ended = false;\n                // Add the sound to the queue to be played on load.\n                var soundId = sound._id;\n                self._queue.push({\n                    event: \"play\",\n                    action: function() {\n                        self.play(soundId);\n                    }\n                });\n                return soundId;\n            }\n            // Don't play the sound if an id was passed and it is already playing.\n            if (id && !sound._paused) {\n                // Trigger the play event, in order to keep iterating through queue.\n                if (!internal) {\n                    self._loadQueue(\"play\");\n                }\n                return sound._id;\n            }\n            // Make sure the AudioContext isn't suspended, and resume it if it is.\n            if (self._webAudio) {\n                Howler1._autoResume();\n            }\n            // Determine how long to play for and where to start playing.\n            var seek = Math.max(0, sound._seek > 0 ? sound._seek : self._sprite[sprite][0] / 1000);\n            var duration = Math.max(0, (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000 - seek);\n            var timeout = duration * 1000 / Math.abs(sound._rate);\n            var start = self._sprite[sprite][0] / 1000;\n            var stop = (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000;\n            sound._sprite = sprite;\n            // Mark the sound as ended instantly so that this async playback\n            // doesn't get grabbed by another call to play while this one waits to start.\n            sound._ended = false;\n            // Update the parameters of the sound.\n            var setParams = function() {\n                sound._paused = false;\n                sound._seek = seek;\n                sound._start = start;\n                sound._stop = stop;\n                sound._loop = !!(sound._loop || self._sprite[sprite][2]);\n            };\n            // End the sound instantly if seek is at the end.\n            if (seek >= stop) {\n                self._ended(sound);\n                return;\n            }\n            // Begin the actual playback.\n            var node = sound._node;\n            if (self._webAudio) {\n                // Fire this when the sound is ready to play to begin Web Audio playback.\n                var playWebAudio = function() {\n                    self._playLock = false;\n                    setParams();\n                    self._refreshBuffer(sound);\n                    // Setup the playback params.\n                    var vol = sound._muted || self._muted ? 0 : sound._volume;\n                    node.gain.setValueAtTime(vol, Howler1.ctx.currentTime);\n                    sound._playStart = Howler1.ctx.currentTime;\n                    // Play the sound using the supported method.\n                    if (typeof node.bufferSource.start === \"undefined\") {\n                        sound._loop ? node.bufferSource.noteGrainOn(0, seek, 86400) : node.bufferSource.noteGrainOn(0, seek, duration);\n                    } else {\n                        sound._loop ? node.bufferSource.start(0, seek, 86400) : node.bufferSource.start(0, seek, duration);\n                    }\n                    // Start a new timer if none is present.\n                    if (timeout !== Infinity) {\n                        self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n                    }\n                    if (!internal) {\n                        setTimeout(function() {\n                            self._emit(\"play\", sound._id);\n                            self._loadQueue();\n                        }, 0);\n                    }\n                };\n                if (Howler1.state === \"running\" && Howler1.ctx.state !== \"interrupted\") {\n                    playWebAudio();\n                } else {\n                    self._playLock = true;\n                    // Wait for the audio context to resume before playing.\n                    self.once(\"resume\", playWebAudio);\n                    // Cancel the end timer.\n                    self._clearTimer(sound._id);\n                }\n            } else {\n                // Fire this when the sound is ready to play to begin HTML5 Audio playback.\n                var playHtml5 = function() {\n                    node.currentTime = seek;\n                    node.muted = sound._muted || self._muted || Howler1._muted || node.muted;\n                    node.volume = sound._volume * Howler1.volume();\n                    node.playbackRate = sound._rate;\n                    // Some browsers will throw an error if this is called without user interaction.\n                    try {\n                        var play = node.play();\n                        // Support older browsers that don't support promises, and thus don't have this issue.\n                        if (play && typeof Promise !== \"undefined\" && (play instanceof Promise || typeof play.then === \"function\")) {\n                            // Implements a lock to prevent DOMException: The play() request was interrupted by a call to pause().\n                            self._playLock = true;\n                            // Set param values immediately.\n                            setParams();\n                            // Releases the lock and executes queued actions.\n                            play.then(function() {\n                                self._playLock = false;\n                                node._unlocked = true;\n                                if (!internal) {\n                                    self._emit(\"play\", sound._id);\n                                } else {\n                                    self._loadQueue();\n                                }\n                            }).catch(function() {\n                                self._playLock = false;\n                                self._emit(\"playerror\", sound._id, \"Playback was unable to start. This is most commonly an issue \" + \"on mobile devices and Chrome where playback was not within a user interaction.\");\n                                // Reset the ended and paused values.\n                                sound._ended = true;\n                                sound._paused = true;\n                            });\n                        } else if (!internal) {\n                            self._playLock = false;\n                            setParams();\n                            self._emit(\"play\", sound._id);\n                        }\n                        // Setting rate before playing won't work in IE, so we set it again here.\n                        node.playbackRate = sound._rate;\n                        // If the node is still paused, then we can assume there was a playback issue.\n                        if (node.paused) {\n                            self._emit(\"playerror\", sound._id, \"Playback was unable to start. This is most commonly an issue \" + \"on mobile devices and Chrome where playback was not within a user interaction.\");\n                            return;\n                        }\n                        // Setup the end timer on sprites or listen for the ended event.\n                        if (sprite !== \"__default\" || sound._loop) {\n                            self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n                        } else {\n                            self._endTimers[sound._id] = function() {\n                                // Fire ended on this audio node.\n                                self._ended(sound);\n                                // Clear this listener.\n                                node.removeEventListener(\"ended\", self._endTimers[sound._id], false);\n                            };\n                            node.addEventListener(\"ended\", self._endTimers[sound._id], false);\n                        }\n                    } catch (err) {\n                        self._emit(\"playerror\", sound._id, err);\n                    }\n                };\n                // If this is streaming audio, make sure the src is set and load again.\n                if (node.src === \"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\") {\n                    node.src = self._src;\n                    node.load();\n                }\n                // Play immediately if ready, or wait for the 'canplaythrough'e vent.\n                var loadedNoReadyState = window && window.ejecta || !node.readyState && Howler1._navigator.isCocoonJS;\n                if (node.readyState >= 3 || loadedNoReadyState) {\n                    playHtml5();\n                } else {\n                    self._playLock = true;\n                    self._state = \"loading\";\n                    var listener = function() {\n                        self._state = \"loaded\";\n                        // Begin playback.\n                        playHtml5();\n                        // Clear this listener.\n                        node.removeEventListener(Howler1._canPlayEvent, listener, false);\n                    };\n                    node.addEventListener(Howler1._canPlayEvent, listener, false);\n                    // Cancel the end timer.\n                    self._clearTimer(sound._id);\n                }\n            }\n            return sound._id;\n        },\n        /**\n     * Pause playback and save current position.\n     * @param  {Number} id The sound ID (empty to pause all in group).\n     * @return {Howl}\n     */ pause: function(id) {\n            var self = this;\n            // If the sound hasn't loaded or a play() promise is pending, add it to the load queue to pause when capable.\n            if (self._state !== \"loaded\" || self._playLock) {\n                self._queue.push({\n                    event: \"pause\",\n                    action: function() {\n                        self.pause(id);\n                    }\n                });\n                return self;\n            }\n            // If no id is passed, get all ID's to be paused.\n            var ids = self._getSoundIds(id);\n            for(var i = 0; i < ids.length; i++){\n                // Clear the end timer.\n                self._clearTimer(ids[i]);\n                // Get the sound.\n                var sound = self._soundById(ids[i]);\n                if (sound && !sound._paused) {\n                    // Reset the seek position.\n                    sound._seek = self.seek(ids[i]);\n                    sound._rateSeek = 0;\n                    sound._paused = true;\n                    // Stop currently running fades.\n                    self._stopFade(ids[i]);\n                    if (sound._node) {\n                        if (self._webAudio) {\n                            // Make sure the sound has been created.\n                            if (!sound._node.bufferSource) {\n                                continue;\n                            }\n                            if (typeof sound._node.bufferSource.stop === \"undefined\") {\n                                sound._node.bufferSource.noteOff(0);\n                            } else {\n                                sound._node.bufferSource.stop(0);\n                            }\n                            // Clean up the buffer source.\n                            self._cleanBuffer(sound._node);\n                        } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n                            sound._node.pause();\n                        }\n                    }\n                }\n                // Fire the pause event, unless `true` is passed as the 2nd argument.\n                if (!arguments[1]) {\n                    self._emit(\"pause\", sound ? sound._id : null);\n                }\n            }\n            return self;\n        },\n        /**\n     * Stop playback and reset to start.\n     * @param  {Number} id The sound ID (empty to stop all in group).\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Howl}\n     */ stop: function(id, internal) {\n            var self = this;\n            // If the sound hasn't loaded, add it to the load queue to stop when capable.\n            if (self._state !== \"loaded\" || self._playLock) {\n                self._queue.push({\n                    event: \"stop\",\n                    action: function() {\n                        self.stop(id);\n                    }\n                });\n                return self;\n            }\n            // If no id is passed, get all ID's to be stopped.\n            var ids = self._getSoundIds(id);\n            for(var i = 0; i < ids.length; i++){\n                // Clear the end timer.\n                self._clearTimer(ids[i]);\n                // Get the sound.\n                var sound = self._soundById(ids[i]);\n                if (sound) {\n                    // Reset the seek position.\n                    sound._seek = sound._start || 0;\n                    sound._rateSeek = 0;\n                    sound._paused = true;\n                    sound._ended = true;\n                    // Stop currently running fades.\n                    self._stopFade(ids[i]);\n                    if (sound._node) {\n                        if (self._webAudio) {\n                            // Make sure the sound's AudioBufferSourceNode has been created.\n                            if (sound._node.bufferSource) {\n                                if (typeof sound._node.bufferSource.stop === \"undefined\") {\n                                    sound._node.bufferSource.noteOff(0);\n                                } else {\n                                    sound._node.bufferSource.stop(0);\n                                }\n                                // Clean up the buffer source.\n                                self._cleanBuffer(sound._node);\n                            }\n                        } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n                            sound._node.currentTime = sound._start || 0;\n                            sound._node.pause();\n                            // If this is a live stream, stop download once the audio is stopped.\n                            if (sound._node.duration === Infinity) {\n                                self._clearSound(sound._node);\n                            }\n                        }\n                    }\n                    if (!internal) {\n                        self._emit(\"stop\", sound._id);\n                    }\n                }\n            }\n            return self;\n        },\n        /**\n     * Mute/unmute a single sound or all sounds in this Howl group.\n     * @param  {Boolean} muted Set to true to mute and false to unmute.\n     * @param  {Number} id    The sound ID to update (omit to mute/unmute all).\n     * @return {Howl}\n     */ mute: function(muted, id) {\n            var self = this;\n            // If the sound hasn't loaded, add it to the load queue to mute when capable.\n            if (self._state !== \"loaded\" || self._playLock) {\n                self._queue.push({\n                    event: \"mute\",\n                    action: function() {\n                        self.mute(muted, id);\n                    }\n                });\n                return self;\n            }\n            // If applying mute/unmute to all sounds, update the group's value.\n            if (typeof id === \"undefined\") {\n                if (typeof muted === \"boolean\") {\n                    self._muted = muted;\n                } else {\n                    return self._muted;\n                }\n            }\n            // If no id is passed, get all ID's to be muted.\n            var ids = self._getSoundIds(id);\n            for(var i = 0; i < ids.length; i++){\n                // Get the sound.\n                var sound = self._soundById(ids[i]);\n                if (sound) {\n                    sound._muted = muted;\n                    // Cancel active fade and set the volume to the end value.\n                    if (sound._interval) {\n                        self._stopFade(sound._id);\n                    }\n                    if (self._webAudio && sound._node) {\n                        sound._node.gain.setValueAtTime(muted ? 0 : sound._volume, Howler1.ctx.currentTime);\n                    } else if (sound._node) {\n                        sound._node.muted = Howler1._muted ? true : muted;\n                    }\n                    self._emit(\"mute\", sound._id);\n                }\n            }\n            return self;\n        },\n        /**\n     * Get/set the volume of this sound or of the Howl group. This method can optionally take 0, 1 or 2 arguments.\n     *   volume() -> Returns the group's volume value.\n     *   volume(id) -> Returns the sound id's current volume.\n     *   volume(vol) -> Sets the volume of all sounds in this Howl group.\n     *   volume(vol, id) -> Sets the volume of passed sound id.\n     * @return {Howl/Number} Returns self or current volume.\n     */ volume: function() {\n            var self = this;\n            var args = arguments;\n            var vol, id;\n            // Determine the values based on arguments.\n            if (args.length === 0) {\n                // Return the value of the groups' volume.\n                return self._volume;\n            } else if (args.length === 1 || args.length === 2 && typeof args[1] === \"undefined\") {\n                // First check if this is an ID, and if not, assume it is a new volume.\n                var ids = self._getSoundIds();\n                var index = ids.indexOf(args[0]);\n                if (index >= 0) {\n                    id = parseInt(args[0], 10);\n                } else {\n                    vol = parseFloat(args[0]);\n                }\n            } else if (args.length >= 2) {\n                vol = parseFloat(args[0]);\n                id = parseInt(args[1], 10);\n            }\n            // Update the volume or return the current volume.\n            var sound;\n            if (typeof vol !== \"undefined\" && vol >= 0 && vol <= 1) {\n                // If the sound hasn't loaded, add it to the load queue to change volume when capable.\n                if (self._state !== \"loaded\" || self._playLock) {\n                    self._queue.push({\n                        event: \"volume\",\n                        action: function() {\n                            self.volume.apply(self, args);\n                        }\n                    });\n                    return self;\n                }\n                // Set the group volume.\n                if (typeof id === \"undefined\") {\n                    self._volume = vol;\n                }\n                // Update one or all volumes.\n                id = self._getSoundIds(id);\n                for(var i = 0; i < id.length; i++){\n                    // Get the sound.\n                    sound = self._soundById(id[i]);\n                    if (sound) {\n                        sound._volume = vol;\n                        // Stop currently running fades.\n                        if (!args[2]) {\n                            self._stopFade(id[i]);\n                        }\n                        if (self._webAudio && sound._node && !sound._muted) {\n                            sound._node.gain.setValueAtTime(vol, Howler1.ctx.currentTime);\n                        } else if (sound._node && !sound._muted) {\n                            sound._node.volume = vol * Howler1.volume();\n                        }\n                        self._emit(\"volume\", sound._id);\n                    }\n                }\n            } else {\n                sound = id ? self._soundById(id) : self._sounds[0];\n                return sound ? sound._volume : 0;\n            }\n            return self;\n        },\n        /**\n     * Fade a currently playing sound between two volumes (if no id is passed, all sounds will fade).\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id (omit to fade all sounds).\n     * @return {Howl}\n     */ fade: function(from, to, len, id) {\n            var self = this;\n            // If the sound hasn't loaded, add it to the load queue to fade when capable.\n            if (self._state !== \"loaded\" || self._playLock) {\n                self._queue.push({\n                    event: \"fade\",\n                    action: function() {\n                        self.fade(from, to, len, id);\n                    }\n                });\n                return self;\n            }\n            // Make sure the to/from/len values are numbers.\n            from = Math.min(Math.max(0, parseFloat(from)), 1);\n            to = Math.min(Math.max(0, parseFloat(to)), 1);\n            len = parseFloat(len);\n            // Set the volume to the start position.\n            self.volume(from, id);\n            // Fade the volume of one or all sounds.\n            var ids = self._getSoundIds(id);\n            for(var i = 0; i < ids.length; i++){\n                // Get the sound.\n                var sound = self._soundById(ids[i]);\n                // Create a linear fade or fall back to timeouts with HTML5 Audio.\n                if (sound) {\n                    // Stop the previous fade if no sprite is being used (otherwise, volume handles this).\n                    if (!id) {\n                        self._stopFade(ids[i]);\n                    }\n                    // If we are using Web Audio, let the native methods do the actual fade.\n                    if (self._webAudio && !sound._muted) {\n                        var currentTime = Howler1.ctx.currentTime;\n                        var end = currentTime + len / 1000;\n                        sound._volume = from;\n                        sound._node.gain.setValueAtTime(from, currentTime);\n                        sound._node.gain.linearRampToValueAtTime(to, end);\n                    }\n                    self._startFadeInterval(sound, from, to, len, ids[i], typeof id === \"undefined\");\n                }\n            }\n            return self;\n        },\n        /**\n     * Starts the internal interval to fade a sound.\n     * @param  {Object} sound Reference to sound to fade.\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id to fade.\n     * @param  {Boolean} isGroup   If true, set the volume on the group.\n     */ _startFadeInterval: function(sound, from, to, len, id, isGroup) {\n            var self = this;\n            var vol = from;\n            var diff = to - from;\n            var steps = Math.abs(diff / 0.01);\n            var stepLen = Math.max(4, steps > 0 ? len / steps : len);\n            var lastTick = Date.now();\n            // Store the value being faded to.\n            sound._fadeTo = to;\n            // Update the volume value on each interval tick.\n            sound._interval = setInterval(function() {\n                // Update the volume based on the time since the last tick.\n                var tick = (Date.now() - lastTick) / len;\n                lastTick = Date.now();\n                vol += diff * tick;\n                // Round to within 2 decimal points.\n                vol = Math.round(vol * 100) / 100;\n                // Make sure the volume is in the right bounds.\n                if (diff < 0) {\n                    vol = Math.max(to, vol);\n                } else {\n                    vol = Math.min(to, vol);\n                }\n                // Change the volume.\n                if (self._webAudio) {\n                    sound._volume = vol;\n                } else {\n                    self.volume(vol, sound._id, true);\n                }\n                // Set the group's volume.\n                if (isGroup) {\n                    self._volume = vol;\n                }\n                // When the fade is complete, stop it and fire event.\n                if (to < from && vol <= to || to > from && vol >= to) {\n                    clearInterval(sound._interval);\n                    sound._interval = null;\n                    sound._fadeTo = null;\n                    self.volume(to, sound._id);\n                    self._emit(\"fade\", sound._id);\n                }\n            }, stepLen);\n        },\n        /**\n     * Internal method that stops the currently playing fade when\n     * a new fade starts, volume is changed or the sound is stopped.\n     * @param  {Number} id The sound id.\n     * @return {Howl}\n     */ _stopFade: function(id) {\n            var self = this;\n            var sound = self._soundById(id);\n            if (sound && sound._interval) {\n                if (self._webAudio) {\n                    sound._node.gain.cancelScheduledValues(Howler1.ctx.currentTime);\n                }\n                clearInterval(sound._interval);\n                sound._interval = null;\n                self.volume(sound._fadeTo, id);\n                sound._fadeTo = null;\n                self._emit(\"fade\", id);\n            }\n            return self;\n        },\n        /**\n     * Get/set the loop parameter on a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   loop() -> Returns the group's loop value.\n     *   loop(id) -> Returns the sound id's loop value.\n     *   loop(loop) -> Sets the loop value for all sounds in this Howl group.\n     *   loop(loop, id) -> Sets the loop value of passed sound id.\n     * @return {Howl/Boolean} Returns self or current loop value.\n     */ loop: function() {\n            var self = this;\n            var args = arguments;\n            var loop, id, sound;\n            // Determine the values for loop and id.\n            if (args.length === 0) {\n                // Return the grou's loop value.\n                return self._loop;\n            } else if (args.length === 1) {\n                if (typeof args[0] === \"boolean\") {\n                    loop = args[0];\n                    self._loop = loop;\n                } else {\n                    // Return this sound's loop value.\n                    sound = self._soundById(parseInt(args[0], 10));\n                    return sound ? sound._loop : false;\n                }\n            } else if (args.length === 2) {\n                loop = args[0];\n                id = parseInt(args[1], 10);\n            }\n            // If no id is passed, get all ID's to be looped.\n            var ids = self._getSoundIds(id);\n            for(var i = 0; i < ids.length; i++){\n                sound = self._soundById(ids[i]);\n                if (sound) {\n                    sound._loop = loop;\n                    if (self._webAudio && sound._node && sound._node.bufferSource) {\n                        sound._node.bufferSource.loop = loop;\n                        if (loop) {\n                            sound._node.bufferSource.loopStart = sound._start || 0;\n                            sound._node.bufferSource.loopEnd = sound._stop;\n                            // If playing, restart playback to ensure looping updates.\n                            if (self.playing(ids[i])) {\n                                self.pause(ids[i], true);\n                                self.play(ids[i], true);\n                            }\n                        }\n                    }\n                }\n            }\n            return self;\n        },\n        /**\n     * Get/set the playback rate of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   rate() -> Returns the first sound node's current playback rate.\n     *   rate(id) -> Returns the sound id's current playback rate.\n     *   rate(rate) -> Sets the playback rate of all sounds in this Howl group.\n     *   rate(rate, id) -> Sets the playback rate of passed sound id.\n     * @return {Howl/Number} Returns self or the current playback rate.\n     */ rate: function() {\n            var self = this;\n            var args = arguments;\n            var rate, id;\n            // Determine the values based on arguments.\n            if (args.length === 0) {\n                // We will simply return the current rate of the first node.\n                id = self._sounds[0]._id;\n            } else if (args.length === 1) {\n                // First check if this is an ID, and if not, assume it is a new rate value.\n                var ids = self._getSoundIds();\n                var index = ids.indexOf(args[0]);\n                if (index >= 0) {\n                    id = parseInt(args[0], 10);\n                } else {\n                    rate = parseFloat(args[0]);\n                }\n            } else if (args.length === 2) {\n                rate = parseFloat(args[0]);\n                id = parseInt(args[1], 10);\n            }\n            // Update the playback rate or return the current value.\n            var sound;\n            if (typeof rate === \"number\") {\n                // If the sound hasn't loaded, add it to the load queue to change playback rate when capable.\n                if (self._state !== \"loaded\" || self._playLock) {\n                    self._queue.push({\n                        event: \"rate\",\n                        action: function() {\n                            self.rate.apply(self, args);\n                        }\n                    });\n                    return self;\n                }\n                // Set the group rate.\n                if (typeof id === \"undefined\") {\n                    self._rate = rate;\n                }\n                // Update one or all volumes.\n                id = self._getSoundIds(id);\n                for(var i = 0; i < id.length; i++){\n                    // Get the sound.\n                    sound = self._soundById(id[i]);\n                    if (sound) {\n                        // Keep track of our position when the rate changed and update the playback\n                        // start position so we can properly adjust the seek position for time elapsed.\n                        if (self.playing(id[i])) {\n                            sound._rateSeek = self.seek(id[i]);\n                            sound._playStart = self._webAudio ? Howler1.ctx.currentTime : sound._playStart;\n                        }\n                        sound._rate = rate;\n                        // Change the playback rate.\n                        if (self._webAudio && sound._node && sound._node.bufferSource) {\n                            sound._node.bufferSource.playbackRate.setValueAtTime(rate, Howler1.ctx.currentTime);\n                        } else if (sound._node) {\n                            sound._node.playbackRate = rate;\n                        }\n                        // Reset the timers.\n                        var seek = self.seek(id[i]);\n                        var duration = (self._sprite[sound._sprite][0] + self._sprite[sound._sprite][1]) / 1000 - seek;\n                        var timeout = duration * 1000 / Math.abs(sound._rate);\n                        // Start a new end timer if sound is already playing.\n                        if (self._endTimers[id[i]] || !sound._paused) {\n                            self._clearTimer(id[i]);\n                            self._endTimers[id[i]] = setTimeout(self._ended.bind(self, sound), timeout);\n                        }\n                        self._emit(\"rate\", sound._id);\n                    }\n                }\n            } else {\n                sound = self._soundById(id);\n                return sound ? sound._rate : self._rate;\n            }\n            return self;\n        },\n        /**\n     * Get/set the seek position of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   seek() -> Returns the first sound node's current seek position.\n     *   seek(id) -> Returns the sound id's current seek position.\n     *   seek(seek) -> Sets the seek position of the first sound node.\n     *   seek(seek, id) -> Sets the seek position of passed sound id.\n     * @return {Howl/Number} Returns self or the current seek position.\n     */ seek: function() {\n            var self = this;\n            var args = arguments;\n            var seek, id;\n            // Determine the values based on arguments.\n            if (args.length === 0) {\n                // We will simply return the current position of the first node.\n                if (self._sounds.length) {\n                    id = self._sounds[0]._id;\n                }\n            } else if (args.length === 1) {\n                // First check if this is an ID, and if not, assume it is a new seek position.\n                var ids = self._getSoundIds();\n                var index = ids.indexOf(args[0]);\n                if (index >= 0) {\n                    id = parseInt(args[0], 10);\n                } else if (self._sounds.length) {\n                    id = self._sounds[0]._id;\n                    seek = parseFloat(args[0]);\n                }\n            } else if (args.length === 2) {\n                seek = parseFloat(args[0]);\n                id = parseInt(args[1], 10);\n            }\n            // If there is no ID, bail out.\n            if (typeof id === \"undefined\") {\n                return 0;\n            }\n            // If the sound hasn't loaded, add it to the load queue to seek when capable.\n            if (typeof seek === \"number\" && (self._state !== \"loaded\" || self._playLock)) {\n                self._queue.push({\n                    event: \"seek\",\n                    action: function() {\n                        self.seek.apply(self, args);\n                    }\n                });\n                return self;\n            }\n            // Get the sound.\n            var sound = self._soundById(id);\n            if (sound) {\n                if (typeof seek === \"number\" && seek >= 0) {\n                    // Pause the sound and update position for restarting playback.\n                    var playing = self.playing(id);\n                    if (playing) {\n                        self.pause(id, true);\n                    }\n                    // Move the position of the track and cancel timer.\n                    sound._seek = seek;\n                    sound._ended = false;\n                    self._clearTimer(id);\n                    // Update the seek position for HTML5 Audio.\n                    if (!self._webAudio && sound._node && !isNaN(sound._node.duration)) {\n                        sound._node.currentTime = seek;\n                    }\n                    // Seek and emit when ready.\n                    var seekAndEmit = function() {\n                        // Restart the playback if the sound was playing.\n                        if (playing) {\n                            self.play(id, true);\n                        }\n                        self._emit(\"seek\", id);\n                    };\n                    // Wait for the play lock to be unset before emitting (HTML5 Audio).\n                    if (playing && !self._webAudio) {\n                        var emitSeek = function() {\n                            if (!self._playLock) {\n                                seekAndEmit();\n                            } else {\n                                setTimeout(emitSeek, 0);\n                            }\n                        };\n                        setTimeout(emitSeek, 0);\n                    } else {\n                        seekAndEmit();\n                    }\n                } else {\n                    if (self._webAudio) {\n                        var realTime = self.playing(id) ? Howler1.ctx.currentTime - sound._playStart : 0;\n                        var rateSeek = sound._rateSeek ? sound._rateSeek - sound._seek : 0;\n                        return sound._seek + (rateSeek + realTime * Math.abs(sound._rate));\n                    } else {\n                        return sound._node.currentTime;\n                    }\n                }\n            }\n            return self;\n        },\n        /**\n     * Check if a specific sound is currently playing or not (if id is provided), or check if at least one of the sounds in the group is playing or not.\n     * @param  {Number}  id The sound id to check. If none is passed, the whole sound group is checked.\n     * @return {Boolean} True if playing and false if not.\n     */ playing: function(id) {\n            var self = this;\n            // Check the passed sound ID (if any).\n            if (typeof id === \"number\") {\n                var sound = self._soundById(id);\n                return sound ? !sound._paused : false;\n            }\n            // Otherwise, loop through all sounds and check if any are playing.\n            for(var i = 0; i < self._sounds.length; i++){\n                if (!self._sounds[i]._paused) {\n                    return true;\n                }\n            }\n            return false;\n        },\n        /**\n     * Get the duration of this sound. Passing a sound id will return the sprite duration.\n     * @param  {Number} id The sound id to check. If none is passed, return full source duration.\n     * @return {Number} Audio duration in seconds.\n     */ duration: function(id) {\n            var self = this;\n            var duration = self._duration;\n            // If we pass an ID, get the sound and return the sprite length.\n            var sound = self._soundById(id);\n            if (sound) {\n                duration = self._sprite[sound._sprite][1] / 1000;\n            }\n            return duration;\n        },\n        /**\n     * Returns the current loaded state of this Howl.\n     * @return {String} 'unloaded', 'loading', 'loaded'\n     */ state: function() {\n            return this._state;\n        },\n        /**\n     * Unload and destroy the current Howl object.\n     * This will immediately stop all sound instances attached to this group.\n     */ unload: function() {\n            var self = this;\n            // Stop playing any active sounds.\n            var sounds = self._sounds;\n            for(var i = 0; i < sounds.length; i++){\n                // Stop the sound if it is currently playing.\n                if (!sounds[i]._paused) {\n                    self.stop(sounds[i]._id);\n                }\n                // Remove the source or disconnect.\n                if (!self._webAudio) {\n                    // Set the source to 0-second silence to stop any downloading (except in IE).\n                    self._clearSound(sounds[i]._node);\n                    // Remove any event listeners.\n                    sounds[i]._node.removeEventListener(\"error\", sounds[i]._errorFn, false);\n                    sounds[i]._node.removeEventListener(Howler1._canPlayEvent, sounds[i]._loadFn, false);\n                    sounds[i]._node.removeEventListener(\"ended\", sounds[i]._endFn, false);\n                    // Release the Audio object back to the pool.\n                    Howler1._releaseHtml5Audio(sounds[i]._node);\n                }\n                // Empty out all of the nodes.\n                delete sounds[i]._node;\n                // Make sure all timers are cleared out.\n                self._clearTimer(sounds[i]._id);\n            }\n            // Remove the references in the global Howler object.\n            var index = Howler1._howls.indexOf(self);\n            if (index >= 0) {\n                Howler1._howls.splice(index, 1);\n            }\n            // Delete this sound from the cache (if no other Howl is using it).\n            var remCache = true;\n            for(i = 0; i < Howler1._howls.length; i++){\n                if (Howler1._howls[i]._src === self._src || self._src.indexOf(Howler1._howls[i]._src) >= 0) {\n                    remCache = false;\n                    break;\n                }\n            }\n            if (cache && remCache) {\n                delete cache[self._src];\n            }\n            // Clear global errors.\n            Howler1.noAudio = false;\n            // Clear out `self`.\n            self._state = \"unloaded\";\n            self._sounds = [];\n            self = null;\n            return null;\n        },\n        /**\n     * Listen to a custom event.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @param  {Number}   once  (INTERNAL) Marks event to fire only once.\n     * @return {Howl}\n     */ on: function(event, fn, id, once) {\n            var self = this;\n            var events = self[\"_on\" + event];\n            if (typeof fn === \"function\") {\n                events.push(once ? {\n                    id: id,\n                    fn: fn,\n                    once: once\n                } : {\n                    id: id,\n                    fn: fn\n                });\n            }\n            return self;\n        },\n        /**\n     * Remove a custom event. Call without parameters to remove all events.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to remove. Leave empty to remove all.\n     * @param  {Number}   id    (optional) Only remove events for this sound.\n     * @return {Howl}\n     */ off: function(event, fn, id) {\n            var self = this;\n            var events = self[\"_on\" + event];\n            var i = 0;\n            // Allow passing just an event and ID.\n            if (typeof fn === \"number\") {\n                id = fn;\n                fn = null;\n            }\n            if (fn || id) {\n                // Loop through event store and remove the passed function.\n                for(i = 0; i < events.length; i++){\n                    var isId = id === events[i].id;\n                    if (fn === events[i].fn && isId || !fn && isId) {\n                        events.splice(i, 1);\n                        break;\n                    }\n                }\n            } else if (event) {\n                // Clear out all events of this type.\n                self[\"_on\" + event] = [];\n            } else {\n                // Clear out all events of every type.\n                var keys = Object.keys(self);\n                for(i = 0; i < keys.length; i++){\n                    if (keys[i].indexOf(\"_on\") === 0 && Array.isArray(self[keys[i]])) {\n                        self[keys[i]] = [];\n                    }\n                }\n            }\n            return self;\n        },\n        /**\n     * Listen to a custom event and remove it once fired.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @return {Howl}\n     */ once: function(event, fn, id) {\n            var self = this;\n            // Setup the event listener.\n            self.on(event, fn, id, 1);\n            return self;\n        },\n        /**\n     * Emit all events of a specific type and pass the sound id.\n     * @param  {String} event Event name.\n     * @param  {Number} id    Sound ID.\n     * @param  {Number} msg   Message to go with event.\n     * @return {Howl}\n     */ _emit: function(event, id, msg) {\n            var self = this;\n            var events = self[\"_on\" + event];\n            // Loop through event store and fire all functions.\n            for(var i = events.length - 1; i >= 0; i--){\n                // Only fire the listener if the correct ID is used.\n                if (!events[i].id || events[i].id === id || event === \"load\") {\n                    setTimeout((function(fn) {\n                        fn.call(this, id, msg);\n                    }).bind(self, events[i].fn), 0);\n                    // If this event was setup with `once`, remove it.\n                    if (events[i].once) {\n                        self.off(event, events[i].fn, events[i].id);\n                    }\n                }\n            }\n            // Pass the event type into load queue so that it can continue stepping.\n            self._loadQueue(event);\n            return self;\n        },\n        /**\n     * Queue of actions initiated before the sound has loaded.\n     * These will be called in sequence, with the next only firing\n     * after the previous has finished executing (even if async like play).\n     * @return {Howl}\n     */ _loadQueue: function(event) {\n            var self = this;\n            if (self._queue.length > 0) {\n                var task = self._queue[0];\n                // Remove this task if a matching event was passed.\n                if (task.event === event) {\n                    self._queue.shift();\n                    self._loadQueue();\n                }\n                // Run the task if no event type is passed.\n                if (!event) {\n                    task.action();\n                }\n            }\n            return self;\n        },\n        /**\n     * Fired when playback ends at the end of the duration.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */ _ended: function(sound) {\n            var self = this;\n            var sprite = sound._sprite;\n            // If we are using IE and there was network latency we may be clipping\n            // audio before it completes playing. Lets check the node to make sure it\n            // believes it has completed, before ending the playback.\n            if (!self._webAudio && sound._node && !sound._node.paused && !sound._node.ended && sound._node.currentTime < sound._stop) {\n                setTimeout(self._ended.bind(self, sound), 100);\n                return self;\n            }\n            // Should this sound loop?\n            var loop = !!(sound._loop || self._sprite[sprite][2]);\n            // Fire the ended event.\n            self._emit(\"end\", sound._id);\n            // Restart the playback for HTML5 Audio loop.\n            if (!self._webAudio && loop) {\n                self.stop(sound._id, true).play(sound._id);\n            }\n            // Restart this timer if on a Web Audio loop.\n            if (self._webAudio && loop) {\n                self._emit(\"play\", sound._id);\n                sound._seek = sound._start || 0;\n                sound._rateSeek = 0;\n                sound._playStart = Howler1.ctx.currentTime;\n                var timeout = (sound._stop - sound._start) * 1000 / Math.abs(sound._rate);\n                self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n            }\n            // Mark the node as paused.\n            if (self._webAudio && !loop) {\n                sound._paused = true;\n                sound._ended = true;\n                sound._seek = sound._start || 0;\n                sound._rateSeek = 0;\n                self._clearTimer(sound._id);\n                // Clean up the buffer source.\n                self._cleanBuffer(sound._node);\n                // Attempt to auto-suspend AudioContext if no sounds are still playing.\n                Howler1._autoSuspend();\n            }\n            // When using a sprite, end the track.\n            if (!self._webAudio && !loop) {\n                self.stop(sound._id, true);\n            }\n            return self;\n        },\n        /**\n     * Clear the end timer for a sound playback.\n     * @param  {Number} id The sound ID.\n     * @return {Howl}\n     */ _clearTimer: function(id) {\n            var self = this;\n            if (self._endTimers[id]) {\n                // Clear the timeout or remove the ended listener.\n                if (typeof self._endTimers[id] !== \"function\") {\n                    clearTimeout(self._endTimers[id]);\n                } else {\n                    var sound = self._soundById(id);\n                    if (sound && sound._node) {\n                        sound._node.removeEventListener(\"ended\", self._endTimers[id], false);\n                    }\n                }\n                delete self._endTimers[id];\n            }\n            return self;\n        },\n        /**\n     * Return the sound identified by this ID, or return null.\n     * @param  {Number} id Sound ID\n     * @return {Object}    Sound object or null.\n     */ _soundById: function(id) {\n            var self = this;\n            // Loop through all sounds and find the one with this ID.\n            for(var i = 0; i < self._sounds.length; i++){\n                if (id === self._sounds[i]._id) {\n                    return self._sounds[i];\n                }\n            }\n            return null;\n        },\n        /**\n     * Return an inactive sound from the pool or create a new one.\n     * @return {Sound} Sound playback object.\n     */ _inactiveSound: function() {\n            var self = this;\n            self._drain();\n            // Find the first inactive node to recycle.\n            for(var i = 0; i < self._sounds.length; i++){\n                if (self._sounds[i]._ended) {\n                    return self._sounds[i].reset();\n                }\n            }\n            // If no inactive node was found, create a new one.\n            return new Sound1(self);\n        },\n        /**\n     * Drain excess inactive sounds from the pool.\n     */ _drain: function() {\n            var self = this;\n            var limit = self._pool;\n            var cnt = 0;\n            var i = 0;\n            // If there are less sounds than the max pool size, we are done.\n            if (self._sounds.length < limit) {\n                return;\n            }\n            // Count the number of inactive sounds.\n            for(i = 0; i < self._sounds.length; i++){\n                if (self._sounds[i]._ended) {\n                    cnt++;\n                }\n            }\n            // Remove excess inactive sounds, going in reverse order.\n            for(i = self._sounds.length - 1; i >= 0; i--){\n                if (cnt <= limit) {\n                    return;\n                }\n                if (self._sounds[i]._ended) {\n                    // Disconnect the audio source when using Web Audio.\n                    if (self._webAudio && self._sounds[i]._node) {\n                        self._sounds[i]._node.disconnect(0);\n                    }\n                    // Remove sounds until we have the pool size.\n                    self._sounds.splice(i, 1);\n                    cnt--;\n                }\n            }\n        },\n        /**\n     * Get all ID's from the sounds pool.\n     * @param  {Number} id Only return one ID if one is passed.\n     * @return {Array}    Array of IDs.\n     */ _getSoundIds: function(id) {\n            var self = this;\n            if (typeof id === \"undefined\") {\n                var ids = [];\n                for(var i = 0; i < self._sounds.length; i++){\n                    ids.push(self._sounds[i]._id);\n                }\n                return ids;\n            } else {\n                return [\n                    id\n                ];\n            }\n        },\n        /**\n     * Load the sound back into the buffer source.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */ _refreshBuffer: function(sound) {\n            var self = this;\n            // Setup the buffer source for playback.\n            sound._node.bufferSource = Howler1.ctx.createBufferSource();\n            sound._node.bufferSource.buffer = cache[self._src];\n            // Connect to the correct node.\n            if (sound._panner) {\n                sound._node.bufferSource.connect(sound._panner);\n            } else {\n                sound._node.bufferSource.connect(sound._node);\n            }\n            // Setup looping and playback rate.\n            sound._node.bufferSource.loop = sound._loop;\n            if (sound._loop) {\n                sound._node.bufferSource.loopStart = sound._start || 0;\n                sound._node.bufferSource.loopEnd = sound._stop || 0;\n            }\n            sound._node.bufferSource.playbackRate.setValueAtTime(sound._rate, Howler1.ctx.currentTime);\n            return self;\n        },\n        /**\n     * Prevent memory leaks by cleaning up the buffer source after playback.\n     * @param  {Object} node Sound's audio node containing the buffer source.\n     * @return {Howl}\n     */ _cleanBuffer: function(node) {\n            var self = this;\n            var isIOS = Howler1._navigator && Howler1._navigator.vendor.indexOf(\"Apple\") >= 0;\n            if (!node.bufferSource) {\n                return self;\n            }\n            if (Howler1._scratchBuffer && node.bufferSource) {\n                node.bufferSource.onended = null;\n                node.bufferSource.disconnect(0);\n                if (isIOS) {\n                    try {\n                        node.bufferSource.buffer = Howler1._scratchBuffer;\n                    } catch (e) {}\n                }\n            }\n            node.bufferSource = null;\n            return self;\n        },\n        /**\n     * Set the source to a 0-second silence to stop any downloading (except in IE).\n     * @param  {Object} node Audio node to clear.\n     */ _clearSound: function(node) {\n            var checkIE = /MSIE |Trident\\//.test(Howler1._navigator && Howler1._navigator.userAgent);\n            if (!checkIE) {\n                node.src = \"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\";\n            }\n        }\n    };\n    /** Single Sound Methods **/ /***************************************************************************/ /**\n   * Setup the sound object, which each node attached to a Howl group is contained in.\n   * @param {Object} howl The Howl parent group.\n   */ var Sound1 = function(howl) {\n        this._parent = howl;\n        this.init();\n    };\n    Sound1.prototype = {\n        /**\n     * Initialize a new Sound object.\n     * @return {Sound}\n     */ init: function() {\n            var self = this;\n            var parent = self._parent;\n            // Setup the default parameters.\n            self._muted = parent._muted;\n            self._loop = parent._loop;\n            self._volume = parent._volume;\n            self._rate = parent._rate;\n            self._seek = 0;\n            self._paused = true;\n            self._ended = true;\n            self._sprite = \"__default\";\n            // Generate a unique ID for this sound.\n            self._id = ++Howler1._counter;\n            // Add itself to the parent's pool.\n            parent._sounds.push(self);\n            // Create the new node.\n            self.create();\n            return self;\n        },\n        /**\n     * Create and setup a new sound object, whether HTML5 Audio or Web Audio.\n     * @return {Sound}\n     */ create: function() {\n            var self = this;\n            var parent = self._parent;\n            var volume = Howler1._muted || self._muted || self._parent._muted ? 0 : self._volume;\n            if (parent._webAudio) {\n                // Create the gain node for controlling volume (the source will connect to this).\n                self._node = typeof Howler1.ctx.createGain === \"undefined\" ? Howler1.ctx.createGainNode() : Howler1.ctx.createGain();\n                self._node.gain.setValueAtTime(volume, Howler1.ctx.currentTime);\n                self._node.paused = true;\n                self._node.connect(Howler1.masterGain);\n            } else if (!Howler1.noAudio) {\n                // Get an unlocked Audio object from the pool.\n                self._node = Howler1._obtainHtml5Audio();\n                // Listen for errors (http://dev.w3.org/html5/spec-author-view/spec.html#mediaerror).\n                self._errorFn = self._errorListener.bind(self);\n                self._node.addEventListener(\"error\", self._errorFn, false);\n                // Listen for 'canplaythrough' event to let us know the sound is ready.\n                self._loadFn = self._loadListener.bind(self);\n                self._node.addEventListener(Howler1._canPlayEvent, self._loadFn, false);\n                // Listen for the 'ended' event on the sound to account for edge-case where\n                // a finite sound has a duration of Infinity.\n                self._endFn = self._endListener.bind(self);\n                self._node.addEventListener(\"ended\", self._endFn, false);\n                // Setup the new audio node.\n                self._node.src = parent._src;\n                self._node.preload = parent._preload === true ? \"auto\" : parent._preload;\n                self._node.volume = volume * Howler1.volume();\n                // Begin loading the source.\n                self._node.load();\n            }\n            return self;\n        },\n        /**\n     * Reset the parameters of this sound to the original state (for recycle).\n     * @return {Sound}\n     */ reset: function() {\n            var self = this;\n            var parent = self._parent;\n            // Reset all of the parameters of this sound.\n            self._muted = parent._muted;\n            self._loop = parent._loop;\n            self._volume = parent._volume;\n            self._rate = parent._rate;\n            self._seek = 0;\n            self._rateSeek = 0;\n            self._paused = true;\n            self._ended = true;\n            self._sprite = \"__default\";\n            // Generate a new ID so that it isn't confused with the previous sound.\n            self._id = ++Howler1._counter;\n            return self;\n        },\n        /**\n     * HTML5 Audio error listener callback.\n     */ _errorListener: function() {\n            var self = this;\n            // Fire an error event and pass back the code.\n            self._parent._emit(\"loaderror\", self._id, self._node.error ? self._node.error.code : 0);\n            // Clear the event listener.\n            self._node.removeEventListener(\"error\", self._errorFn, false);\n        },\n        /**\n     * HTML5 Audio canplaythrough listener callback.\n     */ _loadListener: function() {\n            var self = this;\n            var parent = self._parent;\n            // Round up the duration to account for the lower precision in HTML5 Audio.\n            parent._duration = Math.ceil(self._node.duration * 10) / 10;\n            // Setup a sprite if none is defined.\n            if (Object.keys(parent._sprite).length === 0) {\n                parent._sprite = {\n                    __default: [\n                        0,\n                        parent._duration * 1000\n                    ]\n                };\n            }\n            if (parent._state !== \"loaded\") {\n                parent._state = \"loaded\";\n                parent._emit(\"load\");\n                parent._loadQueue();\n            }\n            // Clear the event listener.\n            self._node.removeEventListener(Howler1._canPlayEvent, self._loadFn, false);\n        },\n        /**\n     * HTML5 Audio ended listener callback.\n     */ _endListener: function() {\n            var self = this;\n            var parent = self._parent;\n            // Only handle the `ended`` event if the duration is Infinity.\n            if (parent._duration === Infinity) {\n                // Update the parent duration to match the real audio duration.\n                // Round up the duration to account for the lower precision in HTML5 Audio.\n                parent._duration = Math.ceil(self._node.duration * 10) / 10;\n                // Update the sprite that corresponds to the real duration.\n                if (parent._sprite.__default[1] === Infinity) {\n                    parent._sprite.__default[1] = parent._duration * 1000;\n                }\n                // Run the regular ended method.\n                parent._ended(self);\n            }\n            // Clear the event listener since the duration is now correct.\n            self._node.removeEventListener(\"ended\", self._endFn, false);\n        }\n    };\n    /** Helper Methods **/ /***************************************************************************/ var cache = {};\n    /**\n   * Buffer a sound from URL, Data URI or cache and decode to audio source (Web Audio API).\n   * @param  {Howl} self\n   */ var loadBuffer = function(self) {\n        var url = self._src;\n        // Check if the buffer has already been cached and use it instead.\n        if (cache[url]) {\n            // Set the duration from the cache.\n            self._duration = cache[url].duration;\n            // Load the sound into this Howl.\n            loadSound(self);\n            return;\n        }\n        if (/^data:[^;]+;base64,/.test(url)) {\n            // Decode the base64 data URI without XHR, since some browsers don't support it.\n            var data = atob(url.split(\",\")[1]);\n            var dataView = new Uint8Array(data.length);\n            for(var i = 0; i < data.length; ++i){\n                dataView[i] = data.charCodeAt(i);\n            }\n            decodeAudioData(dataView.buffer, self);\n        } else {\n            // Load the buffer from the URL.\n            var xhr = new XMLHttpRequest();\n            xhr.open(self._xhr.method, url, true);\n            xhr.withCredentials = self._xhr.withCredentials;\n            xhr.responseType = \"arraybuffer\";\n            // Apply any custom headers to the request.\n            if (self._xhr.headers) {\n                Object.keys(self._xhr.headers).forEach(function(key) {\n                    xhr.setRequestHeader(key, self._xhr.headers[key]);\n                });\n            }\n            xhr.onload = function() {\n                // Make sure we get a successful response back.\n                var code = (xhr.status + \"\")[0];\n                if (code !== \"0\" && code !== \"2\" && code !== \"3\") {\n                    self._emit(\"loaderror\", null, \"Failed loading audio file with status: \" + xhr.status + \".\");\n                    return;\n                }\n                decodeAudioData(xhr.response, self);\n            };\n            xhr.onerror = function() {\n                // If there is an error, switch to HTML5 Audio.\n                if (self._webAudio) {\n                    self._html5 = true;\n                    self._webAudio = false;\n                    self._sounds = [];\n                    delete cache[url];\n                    self.load();\n                }\n            };\n            safeXhrSend(xhr);\n        }\n    };\n    /**\n   * Send the XHR request wrapped in a try/catch.\n   * @param  {Object} xhr XHR to send.\n   */ var safeXhrSend = function(xhr) {\n        try {\n            xhr.send();\n        } catch (e) {\n            xhr.onerror();\n        }\n    };\n    /**\n   * Decode audio data from an array buffer.\n   * @param  {ArrayBuffer} arraybuffer The audio data.\n   * @param  {Howl}        self\n   */ var decodeAudioData = function(arraybuffer, self) {\n        // Fire a load error if something broke.\n        var error = function() {\n            self._emit(\"loaderror\", null, \"Decoding audio data failed.\");\n        };\n        // Load the sound on success.\n        var success = function(buffer) {\n            if (buffer && self._sounds.length > 0) {\n                cache[self._src] = buffer;\n                loadSound(self, buffer);\n            } else {\n                error();\n            }\n        };\n        // Decode the buffer into an audio source.\n        if (typeof Promise !== \"undefined\" && Howler1.ctx.decodeAudioData.length === 1) {\n            Howler1.ctx.decodeAudioData(arraybuffer).then(success).catch(error);\n        } else {\n            Howler1.ctx.decodeAudioData(arraybuffer, success, error);\n        }\n    };\n    /**\n   * Sound is now loaded, so finish setting everything up and fire the loaded event.\n   * @param  {Howl} self\n   * @param  {Object} buffer The decoded buffer sound source.\n   */ var loadSound = function(self, buffer) {\n        // Set the duration.\n        if (buffer && !self._duration) {\n            self._duration = buffer.duration;\n        }\n        // Setup a sprite if none is defined.\n        if (Object.keys(self._sprite).length === 0) {\n            self._sprite = {\n                __default: [\n                    0,\n                    self._duration * 1000\n                ]\n            };\n        }\n        // Fire the loaded event.\n        if (self._state !== \"loaded\") {\n            self._state = \"loaded\";\n            self._emit(\"load\");\n            self._loadQueue();\n        }\n    };\n    /**\n   * Setup the audio context when available, or switch to HTML5 Audio mode.\n   */ var setupAudioContext = function() {\n        // If we have already detected that Web Audio isn't supported, don't run this step again.\n        if (!Howler1.usingWebAudio) {\n            return;\n        }\n        // Check if we are using Web Audio and setup the AudioContext if we are.\n        try {\n            if (typeof AudioContext !== \"undefined\") {\n                Howler1.ctx = new AudioContext();\n            } else if (typeof webkitAudioContext !== \"undefined\") {\n                Howler1.ctx = new webkitAudioContext();\n            } else {\n                Howler1.usingWebAudio = false;\n            }\n        } catch (e) {\n            Howler1.usingWebAudio = false;\n        }\n        // If the audio context creation still failed, set using web audio to false.\n        if (!Howler1.ctx) {\n            Howler1.usingWebAudio = false;\n        }\n        // Check if a webview is being used on iOS8 or earlier (rather than the browser).\n        // If it is, disable Web Audio as it causes crashing.\n        var iOS = /iP(hone|od|ad)/.test(Howler1._navigator && Howler1._navigator.platform);\n        var appVersion = Howler1._navigator && Howler1._navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/);\n        var version = appVersion ? parseInt(appVersion[1], 10) : null;\n        if (iOS && version && version < 9) {\n            var safari = /safari/.test(Howler1._navigator && Howler1._navigator.userAgent.toLowerCase());\n            if (Howler1._navigator && !safari) {\n                Howler1.usingWebAudio = false;\n            }\n        }\n        // Create and expose the master GainNode when using Web Audio (useful for plugins or advanced usage).\n        if (Howler1.usingWebAudio) {\n            Howler1.masterGain = typeof Howler1.ctx.createGain === \"undefined\" ? Howler1.ctx.createGainNode() : Howler1.ctx.createGain();\n            Howler1.masterGain.gain.setValueAtTime(Howler1._muted ? 0 : Howler1._volume, Howler1.ctx.currentTime);\n            Howler1.masterGain.connect(Howler1.ctx.destination);\n        }\n        // Re-run the setup on Howler.\n        Howler1._setup();\n    };\n    // Add support for AMD (Asynchronous Module Definition) libraries such as require.js.\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n            return {\n                Howler: Howler1,\n                Howl: Howl1\n            };\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    }\n    // Add support for CommonJS libraries such as browserify.\n    if (true) {\n        exports.Howler = Howler1;\n        exports.Howl = Howl1;\n    }\n    // Add to global in Node.js (for testing, etc).\n    if (typeof global !== \"undefined\") {\n        global.HowlerGlobal = HowlerGlobal1;\n        global.Howler = Howler1;\n        global.Howl = Howl1;\n        global.Sound = Sound1;\n    } else if (false) {}\n})();\n/*!\n *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.\n *  \n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, James Simpson of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */ (function() {\n    \"use strict\";\n    // Setup default properties.\n    HowlerGlobal.prototype._pos = [\n        0,\n        0,\n        0\n    ];\n    HowlerGlobal.prototype._orientation = [\n        0,\n        0,\n        -1,\n        0,\n        1,\n        0\n    ];\n    /** Global Methods **/ /***************************************************************************/ /**\n   * Helper method to update the stereo panning position of all current Howls.\n   * Future Howls will not use this value unless explicitly set.\n   * @param  {Number} pan A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @return {Howler/Number}     Self or current stereo panning value.\n   */ HowlerGlobal.prototype.stereo = function(pan) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self.ctx || !self.ctx.listener) {\n            return self;\n        }\n        // Loop through all Howls and update their stereo panning.\n        for(var i = self._howls.length - 1; i >= 0; i--){\n            self._howls[i].stereo(pan);\n        }\n        return self;\n    };\n    /**\n   * Get/set the position of the listener in 3D cartesian space. Sounds using\n   * 3D position will be relative to the listener's position.\n   * @param  {Number} x The x-position of the listener.\n   * @param  {Number} y The y-position of the listener.\n   * @param  {Number} z The z-position of the listener.\n   * @return {Howler/Array}   Self or current listener position.\n   */ HowlerGlobal.prototype.pos = function(x, y, z) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self.ctx || !self.ctx.listener) {\n            return self;\n        }\n        // Set the defaults for optional 'y' & 'z'.\n        y = typeof y !== \"number\" ? self._pos[1] : y;\n        z = typeof z !== \"number\" ? self._pos[2] : z;\n        if (typeof x === \"number\") {\n            self._pos = [\n                x,\n                y,\n                z\n            ];\n            if (typeof self.ctx.listener.positionX !== \"undefined\") {\n                self.ctx.listener.positionX.setTargetAtTime(self._pos[0], Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.positionY.setTargetAtTime(self._pos[1], Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.positionZ.setTargetAtTime(self._pos[2], Howler.ctx.currentTime, 0.1);\n            } else {\n                self.ctx.listener.setPosition(self._pos[0], self._pos[1], self._pos[2]);\n            }\n        } else {\n            return self._pos;\n        }\n        return self;\n    };\n    /**\n   * Get/set the direction the listener is pointing in the 3D cartesian space.\n   * A front and up vector must be provided. The front is the direction the\n   * face of the listener is pointing, and up is the direction the top of the\n   * listener is pointing. Thus, these values are expected to be at right angles\n   * from each other.\n   * @param  {Number} x   The x-orientation of the listener.\n   * @param  {Number} y   The y-orientation of the listener.\n   * @param  {Number} z   The z-orientation of the listener.\n   * @param  {Number} xUp The x-orientation of the top of the listener.\n   * @param  {Number} yUp The y-orientation of the top of the listener.\n   * @param  {Number} zUp The z-orientation of the top of the listener.\n   * @return {Howler/Array}     Returns self or the current orientation vectors.\n   */ HowlerGlobal.prototype.orientation = function(x, y, z, xUp, yUp, zUp) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self.ctx || !self.ctx.listener) {\n            return self;\n        }\n        // Set the defaults for optional 'y' & 'z'.\n        var or = self._orientation;\n        y = typeof y !== \"number\" ? or[1] : y;\n        z = typeof z !== \"number\" ? or[2] : z;\n        xUp = typeof xUp !== \"number\" ? or[3] : xUp;\n        yUp = typeof yUp !== \"number\" ? or[4] : yUp;\n        zUp = typeof zUp !== \"number\" ? or[5] : zUp;\n        if (typeof x === \"number\") {\n            self._orientation = [\n                x,\n                y,\n                z,\n                xUp,\n                yUp,\n                zUp\n            ];\n            if (typeof self.ctx.listener.forwardX !== \"undefined\") {\n                self.ctx.listener.forwardX.setTargetAtTime(x, Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.forwardY.setTargetAtTime(y, Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.forwardZ.setTargetAtTime(z, Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.upX.setTargetAtTime(xUp, Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.upY.setTargetAtTime(yUp, Howler.ctx.currentTime, 0.1);\n                self.ctx.listener.upZ.setTargetAtTime(zUp, Howler.ctx.currentTime, 0.1);\n            } else {\n                self.ctx.listener.setOrientation(x, y, z, xUp, yUp, zUp);\n            }\n        } else {\n            return or;\n        }\n        return self;\n    };\n    /** Group Methods **/ /***************************************************************************/ /**\n   * Add new properties to the core init.\n   * @param  {Function} _super Core init method.\n   * @return {Howl}\n   */ Howl.prototype.init = function(_super) {\n        return function(o) {\n            var self = this;\n            // Setup user-defined default properties.\n            self._orientation = o.orientation || [\n                1,\n                0,\n                0\n            ];\n            self._stereo = o.stereo || null;\n            self._pos = o.pos || null;\n            self._pannerAttr = {\n                coneInnerAngle: typeof o.coneInnerAngle !== \"undefined\" ? o.coneInnerAngle : 360,\n                coneOuterAngle: typeof o.coneOuterAngle !== \"undefined\" ? o.coneOuterAngle : 360,\n                coneOuterGain: typeof o.coneOuterGain !== \"undefined\" ? o.coneOuterGain : 0,\n                distanceModel: typeof o.distanceModel !== \"undefined\" ? o.distanceModel : \"inverse\",\n                maxDistance: typeof o.maxDistance !== \"undefined\" ? o.maxDistance : 10000,\n                panningModel: typeof o.panningModel !== \"undefined\" ? o.panningModel : \"HRTF\",\n                refDistance: typeof o.refDistance !== \"undefined\" ? o.refDistance : 1,\n                rolloffFactor: typeof o.rolloffFactor !== \"undefined\" ? o.rolloffFactor : 1\n            };\n            // Setup event listeners.\n            self._onstereo = o.onstereo ? [\n                {\n                    fn: o.onstereo\n                }\n            ] : [];\n            self._onpos = o.onpos ? [\n                {\n                    fn: o.onpos\n                }\n            ] : [];\n            self._onorientation = o.onorientation ? [\n                {\n                    fn: o.onorientation\n                }\n            ] : [];\n            // Complete initilization with howler.js core's init function.\n            return _super.call(this, o);\n        };\n    }(Howl.prototype.init);\n    /**\n   * Get/set the stereo panning of the audio source for this sound or all in the group.\n   * @param  {Number} pan  A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Number}    Returns self or the current stereo panning value.\n   */ Howl.prototype.stereo = function(pan, id) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self._webAudio) {\n            return self;\n        }\n        // If the sound hasn't loaded, add it to the load queue to change stereo pan when capable.\n        if (self._state !== \"loaded\") {\n            self._queue.push({\n                event: \"stereo\",\n                action: function() {\n                    self.stereo(pan, id);\n                }\n            });\n            return self;\n        }\n        // Check for PannerStereoNode support and fallback to PannerNode if it doesn't exist.\n        var pannerType = typeof Howler.ctx.createStereoPanner === \"undefined\" ? \"spatial\" : \"stereo\";\n        // Setup the group's stereo panning if no ID is passed.\n        if (typeof id === \"undefined\") {\n            // Return the group's stereo panning if no parameters are passed.\n            if (typeof pan === \"number\") {\n                self._stereo = pan;\n                self._pos = [\n                    pan,\n                    0,\n                    0\n                ];\n            } else {\n                return self._stereo;\n            }\n        }\n        // Change the streo panning of one or all sounds in group.\n        var ids = self._getSoundIds(id);\n        for(var i = 0; i < ids.length; i++){\n            // Get the sound.\n            var sound = self._soundById(ids[i]);\n            if (sound) {\n                if (typeof pan === \"number\") {\n                    sound._stereo = pan;\n                    sound._pos = [\n                        pan,\n                        0,\n                        0\n                    ];\n                    if (sound._node) {\n                        // If we are falling back, make sure the panningModel is equalpower.\n                        sound._pannerAttr.panningModel = \"equalpower\";\n                        // Check if there is a panner setup and create a new one if not.\n                        if (!sound._panner || !sound._panner.pan) {\n                            setupPanner(sound, pannerType);\n                        }\n                        if (pannerType === \"spatial\") {\n                            if (typeof sound._panner.positionX !== \"undefined\") {\n                                sound._panner.positionX.setValueAtTime(pan, Howler.ctx.currentTime);\n                                sound._panner.positionY.setValueAtTime(0, Howler.ctx.currentTime);\n                                sound._panner.positionZ.setValueAtTime(0, Howler.ctx.currentTime);\n                            } else {\n                                sound._panner.setPosition(pan, 0, 0);\n                            }\n                        } else {\n                            sound._panner.pan.setValueAtTime(pan, Howler.ctx.currentTime);\n                        }\n                    }\n                    self._emit(\"stereo\", sound._id);\n                } else {\n                    return sound._stereo;\n                }\n            }\n        }\n        return self;\n    };\n    /**\n   * Get/set the 3D spatial position of the audio source for this sound or group relative to the global listener.\n   * @param  {Number} x  The x-position of the audio source.\n   * @param  {Number} y  The y-position of the audio source.\n   * @param  {Number} z  The z-position of the audio source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial position: [x, y, z].\n   */ Howl.prototype.pos = function(x, y, z, id) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self._webAudio) {\n            return self;\n        }\n        // If the sound hasn't loaded, add it to the load queue to change position when capable.\n        if (self._state !== \"loaded\") {\n            self._queue.push({\n                event: \"pos\",\n                action: function() {\n                    self.pos(x, y, z, id);\n                }\n            });\n            return self;\n        }\n        // Set the defaults for optional 'y' & 'z'.\n        y = typeof y !== \"number\" ? 0 : y;\n        z = typeof z !== \"number\" ? -0.5 : z;\n        // Setup the group's spatial position if no ID is passed.\n        if (typeof id === \"undefined\") {\n            // Return the group's spatial position if no parameters are passed.\n            if (typeof x === \"number\") {\n                self._pos = [\n                    x,\n                    y,\n                    z\n                ];\n            } else {\n                return self._pos;\n            }\n        }\n        // Change the spatial position of one or all sounds in group.\n        var ids = self._getSoundIds(id);\n        for(var i = 0; i < ids.length; i++){\n            // Get the sound.\n            var sound = self._soundById(ids[i]);\n            if (sound) {\n                if (typeof x === \"number\") {\n                    sound._pos = [\n                        x,\n                        y,\n                        z\n                    ];\n                    if (sound._node) {\n                        // Check if there is a panner setup and create a new one if not.\n                        if (!sound._panner || sound._panner.pan) {\n                            setupPanner(sound, \"spatial\");\n                        }\n                        if (typeof sound._panner.positionX !== \"undefined\") {\n                            sound._panner.positionX.setValueAtTime(x, Howler.ctx.currentTime);\n                            sound._panner.positionY.setValueAtTime(y, Howler.ctx.currentTime);\n                            sound._panner.positionZ.setValueAtTime(z, Howler.ctx.currentTime);\n                        } else {\n                            sound._panner.setPosition(x, y, z);\n                        }\n                    }\n                    self._emit(\"pos\", sound._id);\n                } else {\n                    return sound._pos;\n                }\n            }\n        }\n        return self;\n    };\n    /**\n   * Get/set the direction the audio source is pointing in the 3D cartesian coordinate\n   * space. Depending on how direction the sound is, based on the `cone` attributes,\n   * a sound pointing away from the listener can be quiet or silent.\n   * @param  {Number} x  The x-orientation of the source.\n   * @param  {Number} y  The y-orientation of the source.\n   * @param  {Number} z  The z-orientation of the source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial orientation: [x, y, z].\n   */ Howl.prototype.orientation = function(x, y, z, id) {\n        var self = this;\n        // Stop right here if not using Web Audio.\n        if (!self._webAudio) {\n            return self;\n        }\n        // If the sound hasn't loaded, add it to the load queue to change orientation when capable.\n        if (self._state !== \"loaded\") {\n            self._queue.push({\n                event: \"orientation\",\n                action: function() {\n                    self.orientation(x, y, z, id);\n                }\n            });\n            return self;\n        }\n        // Set the defaults for optional 'y' & 'z'.\n        y = typeof y !== \"number\" ? self._orientation[1] : y;\n        z = typeof z !== \"number\" ? self._orientation[2] : z;\n        // Setup the group's spatial orientation if no ID is passed.\n        if (typeof id === \"undefined\") {\n            // Return the group's spatial orientation if no parameters are passed.\n            if (typeof x === \"number\") {\n                self._orientation = [\n                    x,\n                    y,\n                    z\n                ];\n            } else {\n                return self._orientation;\n            }\n        }\n        // Change the spatial orientation of one or all sounds in group.\n        var ids = self._getSoundIds(id);\n        for(var i = 0; i < ids.length; i++){\n            // Get the sound.\n            var sound = self._soundById(ids[i]);\n            if (sound) {\n                if (typeof x === \"number\") {\n                    sound._orientation = [\n                        x,\n                        y,\n                        z\n                    ];\n                    if (sound._node) {\n                        // Check if there is a panner setup and create a new one if not.\n                        if (!sound._panner) {\n                            // Make sure we have a position to setup the node with.\n                            if (!sound._pos) {\n                                sound._pos = self._pos || [\n                                    0,\n                                    0,\n                                    -0.5\n                                ];\n                            }\n                            setupPanner(sound, \"spatial\");\n                        }\n                        if (typeof sound._panner.orientationX !== \"undefined\") {\n                            sound._panner.orientationX.setValueAtTime(x, Howler.ctx.currentTime);\n                            sound._panner.orientationY.setValueAtTime(y, Howler.ctx.currentTime);\n                            sound._panner.orientationZ.setValueAtTime(z, Howler.ctx.currentTime);\n                        } else {\n                            sound._panner.setOrientation(x, y, z);\n                        }\n                    }\n                    self._emit(\"orientation\", sound._id);\n                } else {\n                    return sound._orientation;\n                }\n            }\n        }\n        return self;\n    };\n    /**\n   * Get/set the panner node's attributes for a sound or group of sounds.\n   * This method can optionall take 0, 1 or 2 arguments.\n   *   pannerAttr() -> Returns the group's values.\n   *   pannerAttr(id) -> Returns the sound id's values.\n   *   pannerAttr(o) -> Set's the values of all sounds in this Howl group.\n   *   pannerAttr(o, id) -> Set's the values of passed sound id.\n   *\n   *   Attributes:\n   *     coneInnerAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      inside of which there will be no volume reduction.\n   *     coneOuterAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      outside of which the volume will be reduced to a constant value of `coneOuterGain`.\n   *     coneOuterGain - (0 by default) A parameter for directional audio sources, this is the gain outside of the\n   *                     `coneOuterAngle`. It is a linear value in the range `[0, 1]`.\n   *     distanceModel - ('inverse' by default) Determines algorithm used to reduce volume as audio moves away from\n   *                     listener. Can be `linear`, `inverse` or `exponential.\n   *     maxDistance - (10000 by default) The maximum distance between source and listener, after which the volume\n   *                   will not be reduced any further.\n   *     refDistance - (1 by default) A reference distance for reducing volume as source moves further from the listener.\n   *                   This is simply a variable of the distance model and has a different effect depending on which model\n   *                   is used and the scale of your coordinates. Generally, volume will be equal to 1 at this distance.\n   *     rolloffFactor - (1 by default) How quickly the volume reduces as source moves from listener. This is simply a\n   *                     variable of the distance model and can be in the range of `[0, 1]` with `linear` and `[0, ∞]`\n   *                     with `inverse` and `exponential`.\n   *     panningModel - ('HRTF' by default) Determines which spatialization algorithm is used to position audio.\n   *                     Can be `HRTF` or `equalpower`.\n   *\n   * @return {Howl/Object} Returns self or current panner attributes.\n   */ Howl.prototype.pannerAttr = function() {\n        var self = this;\n        var args = arguments;\n        var o, id, sound;\n        // Stop right here if not using Web Audio.\n        if (!self._webAudio) {\n            return self;\n        }\n        // Determine the values based on arguments.\n        if (args.length === 0) {\n            // Return the group's panner attribute values.\n            return self._pannerAttr;\n        } else if (args.length === 1) {\n            if (typeof args[0] === \"object\") {\n                o = args[0];\n                // Set the grou's panner attribute values.\n                if (typeof id === \"undefined\") {\n                    if (!o.pannerAttr) {\n                        o.pannerAttr = {\n                            coneInnerAngle: o.coneInnerAngle,\n                            coneOuterAngle: o.coneOuterAngle,\n                            coneOuterGain: o.coneOuterGain,\n                            distanceModel: o.distanceModel,\n                            maxDistance: o.maxDistance,\n                            refDistance: o.refDistance,\n                            rolloffFactor: o.rolloffFactor,\n                            panningModel: o.panningModel\n                        };\n                    }\n                    self._pannerAttr = {\n                        coneInnerAngle: typeof o.pannerAttr.coneInnerAngle !== \"undefined\" ? o.pannerAttr.coneInnerAngle : self._coneInnerAngle,\n                        coneOuterAngle: typeof o.pannerAttr.coneOuterAngle !== \"undefined\" ? o.pannerAttr.coneOuterAngle : self._coneOuterAngle,\n                        coneOuterGain: typeof o.pannerAttr.coneOuterGain !== \"undefined\" ? o.pannerAttr.coneOuterGain : self._coneOuterGain,\n                        distanceModel: typeof o.pannerAttr.distanceModel !== \"undefined\" ? o.pannerAttr.distanceModel : self._distanceModel,\n                        maxDistance: typeof o.pannerAttr.maxDistance !== \"undefined\" ? o.pannerAttr.maxDistance : self._maxDistance,\n                        refDistance: typeof o.pannerAttr.refDistance !== \"undefined\" ? o.pannerAttr.refDistance : self._refDistance,\n                        rolloffFactor: typeof o.pannerAttr.rolloffFactor !== \"undefined\" ? o.pannerAttr.rolloffFactor : self._rolloffFactor,\n                        panningModel: typeof o.pannerAttr.panningModel !== \"undefined\" ? o.pannerAttr.panningModel : self._panningModel\n                    };\n                }\n            } else {\n                // Return this sound's panner attribute values.\n                sound = self._soundById(parseInt(args[0], 10));\n                return sound ? sound._pannerAttr : self._pannerAttr;\n            }\n        } else if (args.length === 2) {\n            o = args[0];\n            id = parseInt(args[1], 10);\n        }\n        // Update the values of the specified sounds.\n        var ids = self._getSoundIds(id);\n        for(var i = 0; i < ids.length; i++){\n            sound = self._soundById(ids[i]);\n            if (sound) {\n                // Merge the new values into the sound.\n                var pa = sound._pannerAttr;\n                pa = {\n                    coneInnerAngle: typeof o.coneInnerAngle !== \"undefined\" ? o.coneInnerAngle : pa.coneInnerAngle,\n                    coneOuterAngle: typeof o.coneOuterAngle !== \"undefined\" ? o.coneOuterAngle : pa.coneOuterAngle,\n                    coneOuterGain: typeof o.coneOuterGain !== \"undefined\" ? o.coneOuterGain : pa.coneOuterGain,\n                    distanceModel: typeof o.distanceModel !== \"undefined\" ? o.distanceModel : pa.distanceModel,\n                    maxDistance: typeof o.maxDistance !== \"undefined\" ? o.maxDistance : pa.maxDistance,\n                    refDistance: typeof o.refDistance !== \"undefined\" ? o.refDistance : pa.refDistance,\n                    rolloffFactor: typeof o.rolloffFactor !== \"undefined\" ? o.rolloffFactor : pa.rolloffFactor,\n                    panningModel: typeof o.panningModel !== \"undefined\" ? o.panningModel : pa.panningModel\n                };\n                // Create a new panner node if one doesn't already exist.\n                var panner = sound._panner;\n                if (!panner) {\n                    // Make sure we have a position to setup the node with.\n                    if (!sound._pos) {\n                        sound._pos = self._pos || [\n                            0,\n                            0,\n                            -0.5\n                        ];\n                    }\n                    // Create a new panner node.\n                    setupPanner(sound, \"spatial\");\n                    panner = sound._panner;\n                }\n                // Update the panner values or create a new panner if none exists.\n                panner.coneInnerAngle = pa.coneInnerAngle;\n                panner.coneOuterAngle = pa.coneOuterAngle;\n                panner.coneOuterGain = pa.coneOuterGain;\n                panner.distanceModel = pa.distanceModel;\n                panner.maxDistance = pa.maxDistance;\n                panner.refDistance = pa.refDistance;\n                panner.rolloffFactor = pa.rolloffFactor;\n                panner.panningModel = pa.panningModel;\n            }\n        }\n        return self;\n    };\n    /** Single Sound Methods **/ /***************************************************************************/ /**\n   * Add new properties to the core Sound init.\n   * @param  {Function} _super Core Sound init method.\n   * @return {Sound}\n   */ Sound.prototype.init = function(_super) {\n        return function() {\n            var self = this;\n            var parent = self._parent;\n            // Setup user-defined default properties.\n            self._orientation = parent._orientation;\n            self._stereo = parent._stereo;\n            self._pos = parent._pos;\n            self._pannerAttr = parent._pannerAttr;\n            // Complete initilization with howler.js core Sound's init function.\n            _super.call(this);\n            // If a stereo or position was specified, set it up.\n            if (self._stereo) {\n                parent.stereo(self._stereo);\n            } else if (self._pos) {\n                parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n            }\n        };\n    }(Sound.prototype.init);\n    /**\n   * Override the Sound.reset method to clean up properties from the spatial plugin.\n   * @param  {Function} _super Sound reset method.\n   * @return {Sound}\n   */ Sound.prototype.reset = function(_super) {\n        return function() {\n            var self = this;\n            var parent = self._parent;\n            // Reset all spatial plugin properties on this sound.\n            self._orientation = parent._orientation;\n            self._stereo = parent._stereo;\n            self._pos = parent._pos;\n            self._pannerAttr = parent._pannerAttr;\n            // If a stereo or position was specified, set it up.\n            if (self._stereo) {\n                parent.stereo(self._stereo);\n            } else if (self._pos) {\n                parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n            } else if (self._panner) {\n                // Disconnect the panner.\n                self._panner.disconnect(0);\n                self._panner = undefined;\n                parent._refreshBuffer(self);\n            }\n            // Complete resetting of the sound.\n            return _super.call(this);\n        };\n    }(Sound.prototype.reset);\n    /** Helper Methods **/ /***************************************************************************/ /**\n   * Create a new panner node and save it on the sound.\n   * @param  {Sound} sound Specific sound to setup panning on.\n   * @param {String} type Type of panner to create: 'stereo' or 'spatial'.\n   */ var setupPanner = function(sound, type) {\n        type = type || \"spatial\";\n        // Create the new panner node.\n        if (type === \"spatial\") {\n            sound._panner = Howler.ctx.createPanner();\n            sound._panner.coneInnerAngle = sound._pannerAttr.coneInnerAngle;\n            sound._panner.coneOuterAngle = sound._pannerAttr.coneOuterAngle;\n            sound._panner.coneOuterGain = sound._pannerAttr.coneOuterGain;\n            sound._panner.distanceModel = sound._pannerAttr.distanceModel;\n            sound._panner.maxDistance = sound._pannerAttr.maxDistance;\n            sound._panner.refDistance = sound._pannerAttr.refDistance;\n            sound._panner.rolloffFactor = sound._pannerAttr.rolloffFactor;\n            sound._panner.panningModel = sound._pannerAttr.panningModel;\n            if (typeof sound._panner.positionX !== \"undefined\") {\n                sound._panner.positionX.setValueAtTime(sound._pos[0], Howler.ctx.currentTime);\n                sound._panner.positionY.setValueAtTime(sound._pos[1], Howler.ctx.currentTime);\n                sound._panner.positionZ.setValueAtTime(sound._pos[2], Howler.ctx.currentTime);\n            } else {\n                sound._panner.setPosition(sound._pos[0], sound._pos[1], sound._pos[2]);\n            }\n            if (typeof sound._panner.orientationX !== \"undefined\") {\n                sound._panner.orientationX.setValueAtTime(sound._orientation[0], Howler.ctx.currentTime);\n                sound._panner.orientationY.setValueAtTime(sound._orientation[1], Howler.ctx.currentTime);\n                sound._panner.orientationZ.setValueAtTime(sound._orientation[2], Howler.ctx.currentTime);\n            } else {\n                sound._panner.setOrientation(sound._orientation[0], sound._orientation[1], sound._orientation[2]);\n            }\n        } else {\n            sound._panner = Howler.ctx.createStereoPanner();\n            sound._panner.pan.setValueAtTime(sound._stereo, Howler.ctx.currentTime);\n        }\n        sound._panner.connect(sound._node);\n        // Update the connections.\n        if (!sound._paused) {\n            sound._parent.pause(sound._id, true).play(sound._id, true);\n        }\n    };\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/howler/dist/howler.js\n");

/***/ })

};
;