"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/i18next";
exports.ids = ["vendor-chunks/i18next"];
exports.modules = {

/***/ "(ssr)/./node_modules/i18next/dist/esm/i18next.js":
/*!**************************************************!*\
  !*** ./node_modules/i18next/dist/esm/i18next.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   changeLanguage: () => (/* binding */ changeLanguage),\n/* harmony export */   createInstance: () => (/* binding */ createInstance),\n/* harmony export */   \"default\": () => (/* binding */ instance),\n/* harmony export */   dir: () => (/* binding */ dir),\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   getFixedT: () => (/* binding */ getFixedT),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   loadResources: () => (/* binding */ loadResources),\n/* harmony export */   reloadResources: () => (/* binding */ reloadResources),\n/* harmony export */   setDefaultNamespace: () => (/* binding */ setDefaultNamespace),\n/* harmony export */   t: () => (/* binding */ t),\n/* harmony export */   use: () => (/* binding */ use)\n/* harmony export */ });\nconst isString = (obj)=>typeof obj === \"string\";\nconst defer = ()=>{\n    let res;\n    let rej;\n    const promise = new Promise((resolve, reject)=>{\n        res = resolve;\n        rej = reject;\n    });\n    promise.resolve = res;\n    promise.reject = rej;\n    return promise;\n};\nconst makeString = (object)=>{\n    if (object == null) return \"\";\n    return \"\" + object;\n};\nconst copy = (a, s, t)=>{\n    a.forEach((m)=>{\n        if (s[m]) t[m] = s[m];\n    });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = (key)=>key && key.indexOf(\"###\") > -1 ? key.replace(lastOfPathSeparatorRegExp, \".\") : key;\nconst canNotTraverseDeeper = (object)=>!object || isString(object);\nconst getLastOfPath = (object, path, Empty)=>{\n    const stack = !isString(path) ? path : path.split(\".\");\n    let stackIndex = 0;\n    while(stackIndex < stack.length - 1){\n        if (canNotTraverseDeeper(object)) return {};\n        const key = cleanKey(stack[stackIndex]);\n        if (!object[key] && Empty) object[key] = new Empty();\n        if (Object.prototype.hasOwnProperty.call(object, key)) {\n            object = object[key];\n        } else {\n            object = {};\n        }\n        ++stackIndex;\n    }\n    if (canNotTraverseDeeper(object)) return {};\n    return {\n        obj: object,\n        k: cleanKey(stack[stackIndex])\n    };\n};\nconst setPath = (object, path, newValue)=>{\n    const { obj, k } = getLastOfPath(object, path, Object);\n    if (obj !== undefined || path.length === 1) {\n        obj[k] = newValue;\n        return;\n    }\n    let e = path[path.length - 1];\n    let p = path.slice(0, path.length - 1);\n    let last = getLastOfPath(object, p, Object);\n    while(last.obj === undefined && p.length){\n        e = `${p[p.length - 1]}.${e}`;\n        p = p.slice(0, p.length - 1);\n        last = getLastOfPath(object, p, Object);\n        if (last && last.obj && typeof last.obj[`${last.k}.${e}`] !== \"undefined\") {\n            last.obj = undefined;\n        }\n    }\n    last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat)=>{\n    const { obj, k } = getLastOfPath(object, path, Object);\n    obj[k] = obj[k] || [];\n    obj[k].push(newValue);\n};\nconst getPath = (object, path)=>{\n    const { obj, k } = getLastOfPath(object, path);\n    if (!obj) return undefined;\n    return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key)=>{\n    const value = getPath(data, key);\n    if (value !== undefined) {\n        return value;\n    }\n    return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite)=>{\n    for(const prop in source){\n        if (prop !== \"__proto__\" && prop !== \"constructor\") {\n            if (prop in target) {\n                if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n                    if (overwrite) target[prop] = source[prop];\n                } else {\n                    deepExtend(target[prop], source[prop], overwrite);\n                }\n            } else {\n                target[prop] = source[prop];\n            }\n        }\n    }\n    return target;\n};\nconst regexEscape = (str)=>str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\");\nvar _entityMap = {\n    \"&\": \"&amp;\",\n    \"<\": \"&lt;\",\n    \">\": \"&gt;\",\n    '\"': \"&quot;\",\n    \"'\": \"&#39;\",\n    \"/\": \"&#x2F;\"\n};\nconst escape = (data)=>{\n    if (isString(data)) {\n        return data.replace(/[&<>\"'\\/]/g, (s)=>_entityMap[s]);\n    }\n    return data;\n};\nclass RegExpCache {\n    constructor(capacity){\n        this.capacity = capacity;\n        this.regExpMap = new Map();\n        this.regExpQueue = [];\n    }\n    getRegExp(pattern) {\n        const regExpFromCache = this.regExpMap.get(pattern);\n        if (regExpFromCache !== undefined) {\n            return regExpFromCache;\n        }\n        const regExpNew = new RegExp(pattern);\n        if (this.regExpQueue.length === this.capacity) {\n            this.regExpMap.delete(this.regExpQueue.shift());\n        }\n        this.regExpMap.set(pattern, regExpNew);\n        this.regExpQueue.push(pattern);\n        return regExpNew;\n    }\n}\nconst chars = [\n    \" \",\n    \",\",\n    \"?\",\n    \"!\",\n    \";\"\n];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator)=>{\n    nsSeparator = nsSeparator || \"\";\n    keySeparator = keySeparator || \"\";\n    const possibleChars = chars.filter((c)=>nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n    if (possibleChars.length === 0) return true;\n    const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map((c)=>c === \"?\" ? \"\\\\?\" : c).join(\"|\")})`);\n    let matched = !r.test(key);\n    if (!matched) {\n        const ki = key.indexOf(keySeparator);\n        if (ki > 0 && !r.test(key.substring(0, ki))) {\n            matched = true;\n        }\n    }\n    return matched;\n};\nconst deepFind = function(obj, path) {\n    let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \".\";\n    if (!obj) return undefined;\n    if (obj[path]) return obj[path];\n    const tokens = path.split(keySeparator);\n    let current = obj;\n    for(let i = 0; i < tokens.length;){\n        if (!current || typeof current !== \"object\") {\n            return undefined;\n        }\n        let next;\n        let nextPath = \"\";\n        for(let j = i; j < tokens.length; ++j){\n            if (j !== i) {\n                nextPath += keySeparator;\n            }\n            nextPath += tokens[j];\n            next = current[nextPath];\n            if (next !== undefined) {\n                if ([\n                    \"string\",\n                    \"number\",\n                    \"boolean\"\n                ].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n                    continue;\n                }\n                i += j - i + 1;\n                break;\n            }\n        }\n        current = next;\n    }\n    return current;\n};\nconst getCleanedCode = (code)=>code && code.replace(\"_\", \"-\");\nconst consoleLogger = {\n    type: \"logger\",\n    log (args) {\n        this.output(\"log\", args);\n    },\n    warn (args) {\n        this.output(\"warn\", args);\n    },\n    error (args) {\n        this.output(\"error\", args);\n    },\n    output (type, args) {\n        if (console && console[type]) console[type].apply(console, args);\n    }\n};\nclass Logger {\n    constructor(concreteLogger){\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        this.init(concreteLogger, options);\n    }\n    init(concreteLogger) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        this.prefix = options.prefix || \"i18next:\";\n        this.logger = concreteLogger || consoleLogger;\n        this.options = options;\n        this.debug = options.debug;\n    }\n    log() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return this.forward(args, \"log\", \"\", true);\n    }\n    warn() {\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return this.forward(args, \"warn\", \"\", true);\n    }\n    error() {\n        for(var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){\n            args[_key3] = arguments[_key3];\n        }\n        return this.forward(args, \"error\", \"\");\n    }\n    deprecate() {\n        for(var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){\n            args[_key4] = arguments[_key4];\n        }\n        return this.forward(args, \"warn\", \"WARNING DEPRECATED: \", true);\n    }\n    forward(args, lvl, prefix, debugOnly) {\n        if (debugOnly && !this.debug) return null;\n        if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n        return this.logger[lvl](args);\n    }\n    create(moduleName) {\n        return new Logger(this.logger, {\n            ...{\n                prefix: `${this.prefix}:${moduleName}:`\n            },\n            ...this.options\n        });\n    }\n    clone(options) {\n        options = options || this.options;\n        options.prefix = options.prefix || this.prefix;\n        return new Logger(this.logger, options);\n    }\n}\nvar baseLogger = new Logger();\nclass EventEmitter {\n    constructor(){\n        this.observers = {};\n    }\n    on(events, listener) {\n        events.split(\" \").forEach((event)=>{\n            if (!this.observers[event]) this.observers[event] = new Map();\n            const numListeners = this.observers[event].get(listener) || 0;\n            this.observers[event].set(listener, numListeners + 1);\n        });\n        return this;\n    }\n    off(event, listener) {\n        if (!this.observers[event]) return;\n        if (!listener) {\n            delete this.observers[event];\n            return;\n        }\n        this.observers[event].delete(listener);\n    }\n    emit(event) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        if (this.observers[event]) {\n            const cloned = Array.from(this.observers[event].entries());\n            cloned.forEach((_ref)=>{\n                let [observer, numTimesAdded] = _ref;\n                for(let i = 0; i < numTimesAdded; i++){\n                    observer(...args);\n                }\n            });\n        }\n        if (this.observers[\"*\"]) {\n            const cloned = Array.from(this.observers[\"*\"].entries());\n            cloned.forEach((_ref2)=>{\n                let [observer, numTimesAdded] = _ref2;\n                for(let i = 0; i < numTimesAdded; i++){\n                    observer.apply(observer, [\n                        event,\n                        ...args\n                    ]);\n                }\n            });\n        }\n    }\n}\nclass ResourceStore extends EventEmitter {\n    constructor(data){\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n            ns: [\n                \"translation\"\n            ],\n            defaultNS: \"translation\"\n        };\n        super();\n        this.data = data || {};\n        this.options = options;\n        if (this.options.keySeparator === undefined) {\n            this.options.keySeparator = \".\";\n        }\n        if (this.options.ignoreJSONStructure === undefined) {\n            this.options.ignoreJSONStructure = true;\n        }\n    }\n    addNamespaces(ns) {\n        if (this.options.ns.indexOf(ns) < 0) {\n            this.options.ns.push(ns);\n        }\n    }\n    removeNamespaces(ns) {\n        const index = this.options.ns.indexOf(ns);\n        if (index > -1) {\n            this.options.ns.splice(index, 1);\n        }\n    }\n    getResource(lng, ns, key) {\n        let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n        const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n        const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n        let path;\n        if (lng.indexOf(\".\") > -1) {\n            path = lng.split(\".\");\n        } else {\n            path = [\n                lng,\n                ns\n            ];\n            if (key) {\n                if (Array.isArray(key)) {\n                    path.push(...key);\n                } else if (isString(key) && keySeparator) {\n                    path.push(...key.split(keySeparator));\n                } else {\n                    path.push(key);\n                }\n            }\n        }\n        const result = getPath(this.data, path);\n        if (!result && !ns && !key && lng.indexOf(\".\") > -1) {\n            lng = path[0];\n            ns = path[1];\n            key = path.slice(2).join(\".\");\n        }\n        if (result || !ignoreJSONStructure || !isString(key)) return result;\n        return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n    }\n    addResource(lng, ns, key, value) {\n        let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n            silent: false\n        };\n        const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n        let path = [\n            lng,\n            ns\n        ];\n        if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n        if (lng.indexOf(\".\") > -1) {\n            path = lng.split(\".\");\n            value = ns;\n            ns = path[1];\n        }\n        this.addNamespaces(ns);\n        setPath(this.data, path, value);\n        if (!options.silent) this.emit(\"added\", lng, ns, key, value);\n    }\n    addResources(lng, ns, resources) {\n        let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n            silent: false\n        };\n        for(const m in resources){\n            if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n                silent: true\n            });\n        }\n        if (!options.silent) this.emit(\"added\", lng, ns, resources);\n    }\n    addResourceBundle(lng, ns, resources, deep, overwrite) {\n        let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n            silent: false,\n            skipCopy: false\n        };\n        let path = [\n            lng,\n            ns\n        ];\n        if (lng.indexOf(\".\") > -1) {\n            path = lng.split(\".\");\n            deep = resources;\n            resources = ns;\n            ns = path[1];\n        }\n        this.addNamespaces(ns);\n        let pack = getPath(this.data, path) || {};\n        if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n        if (deep) {\n            deepExtend(pack, resources, overwrite);\n        } else {\n            pack = {\n                ...pack,\n                ...resources\n            };\n        }\n        setPath(this.data, path, pack);\n        if (!options.silent) this.emit(\"added\", lng, ns, resources);\n    }\n    removeResourceBundle(lng, ns) {\n        if (this.hasResourceBundle(lng, ns)) {\n            delete this.data[lng][ns];\n        }\n        this.removeNamespaces(ns);\n        this.emit(\"removed\", lng, ns);\n    }\n    hasResourceBundle(lng, ns) {\n        return this.getResource(lng, ns) !== undefined;\n    }\n    getResourceBundle(lng, ns) {\n        if (!ns) ns = this.options.defaultNS;\n        if (this.options.compatibilityAPI === \"v1\") return {\n            ...{},\n            ...this.getResource(lng, ns)\n        };\n        return this.getResource(lng, ns);\n    }\n    getDataByLanguage(lng) {\n        return this.data[lng];\n    }\n    hasLanguageSomeTranslations(lng) {\n        const data = this.getDataByLanguage(lng);\n        const n = data && Object.keys(data) || [];\n        return !!n.find((v)=>data[v] && Object.keys(data[v]).length > 0);\n    }\n    toJSON() {\n        return this.data;\n    }\n}\nvar postProcessor = {\n    processors: {},\n    addPostProcessor (module) {\n        this.processors[module.name] = module;\n    },\n    handle (processors, value, key, options, translator) {\n        processors.forEach((processor)=>{\n            if (this.processors[processor]) value = this.processors[processor].process(value, key, options, translator);\n        });\n        return value;\n    }\n};\nconst checkedLoadedFor = {};\nclass Translator extends EventEmitter {\n    constructor(services){\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        super();\n        copy([\n            \"resourceStore\",\n            \"languageUtils\",\n            \"pluralResolver\",\n            \"interpolator\",\n            \"backendConnector\",\n            \"i18nFormat\",\n            \"utils\"\n        ], services, this);\n        this.options = options;\n        if (this.options.keySeparator === undefined) {\n            this.options.keySeparator = \".\";\n        }\n        this.logger = baseLogger.create(\"translator\");\n    }\n    changeLanguage(lng) {\n        if (lng) this.language = lng;\n    }\n    exists(key) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n            interpolation: {}\n        };\n        if (key === undefined || key === null) {\n            return false;\n        }\n        const resolved = this.resolve(key, options);\n        return resolved && resolved.res !== undefined;\n    }\n    extractFromKey(key, options) {\n        let nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n        if (nsSeparator === undefined) nsSeparator = \":\";\n        const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n        let namespaces = options.ns || this.options.defaultNS || [];\n        const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n        const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !options.keySeparator && !this.options.userDefinedNsSeparator && !options.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n        if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n            const m = key.match(this.interpolator.nestingRegexp);\n            if (m && m.length > 0) {\n                return {\n                    key,\n                    namespaces: isString(namespaces) ? [\n                        namespaces\n                    ] : namespaces\n                };\n            }\n            const parts = key.split(nsSeparator);\n            if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n            key = parts.join(keySeparator);\n        }\n        return {\n            key,\n            namespaces: isString(namespaces) ? [\n                namespaces\n            ] : namespaces\n        };\n    }\n    translate(keys, options, lastKey) {\n        if (typeof options !== \"object\" && this.options.overloadTranslationOptionHandler) {\n            options = this.options.overloadTranslationOptionHandler(arguments);\n        }\n        if (typeof options === \"object\") options = {\n            ...options\n        };\n        if (!options) options = {};\n        if (keys === undefined || keys === null) return \"\";\n        if (!Array.isArray(keys)) keys = [\n            String(keys)\n        ];\n        const returnDetails = options.returnDetails !== undefined ? options.returnDetails : this.options.returnDetails;\n        const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n        const { key, namespaces } = this.extractFromKey(keys[keys.length - 1], options);\n        const namespace = namespaces[namespaces.length - 1];\n        const lng = options.lng || this.language;\n        const appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n        if (lng && lng.toLowerCase() === \"cimode\") {\n            if (appendNamespaceToCIMode) {\n                const nsSeparator = options.nsSeparator || this.options.nsSeparator;\n                if (returnDetails) {\n                    return {\n                        res: `${namespace}${nsSeparator}${key}`,\n                        usedKey: key,\n                        exactUsedKey: key,\n                        usedLng: lng,\n                        usedNS: namespace,\n                        usedParams: this.getUsedParamsDetails(options)\n                    };\n                }\n                return `${namespace}${nsSeparator}${key}`;\n            }\n            if (returnDetails) {\n                return {\n                    res: key,\n                    usedKey: key,\n                    exactUsedKey: key,\n                    usedLng: lng,\n                    usedNS: namespace,\n                    usedParams: this.getUsedParamsDetails(options)\n                };\n            }\n            return key;\n        }\n        const resolved = this.resolve(keys, options);\n        let res = resolved && resolved.res;\n        const resUsedKey = resolved && resolved.usedKey || key;\n        const resExactUsedKey = resolved && resolved.exactUsedKey || key;\n        const resType = Object.prototype.toString.apply(res);\n        const noObject = [\n            \"[object Number]\",\n            \"[object Function]\",\n            \"[object RegExp]\"\n        ];\n        const joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n        const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n        const handleAsObject = !isString(res) && typeof res !== \"boolean\" && typeof res !== \"number\";\n        if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(res))) {\n            if (!options.returnObjects && !this.options.returnObjects) {\n                if (!this.options.returnedObjectHandler) {\n                    this.logger.warn(\"accessing an object - but returnObjects options is not enabled!\");\n                }\n                const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, {\n                    ...options,\n                    ns: namespaces\n                }) : `key '${key} (${this.language})' returned an object instead of string.`;\n                if (returnDetails) {\n                    resolved.res = r;\n                    resolved.usedParams = this.getUsedParamsDetails(options);\n                    return resolved;\n                }\n                return r;\n            }\n            if (keySeparator) {\n                const resTypeIsArray = Array.isArray(res);\n                const copy = resTypeIsArray ? [] : {};\n                const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n                for(const m in res){\n                    if (Object.prototype.hasOwnProperty.call(res, m)) {\n                        const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n                        copy[m] = this.translate(deepKey, {\n                            ...options,\n                            ...{\n                                joinArrays: false,\n                                ns: namespaces\n                            }\n                        });\n                        if (copy[m] === deepKey) copy[m] = res[m];\n                    }\n                }\n                res = copy;\n            }\n        } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n            res = res.join(joinArrays);\n            if (res) res = this.extendTranslation(res, keys, options, lastKey);\n        } else {\n            let usedDefault = false;\n            let usedKey = false;\n            const needsPluralHandling = options.count !== undefined && !isString(options.count);\n            const hasDefaultValue = Translator.hasDefaultValue(options);\n            const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, options) : \"\";\n            const defaultValueSuffixOrdinalFallback = options.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, {\n                ordinal: false\n            }) : \"\";\n            const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n            const defaultValue = needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] || options[`defaultValue${defaultValueSuffix}`] || options[`defaultValue${defaultValueSuffixOrdinalFallback}`] || options.defaultValue;\n            if (!this.isValidLookup(res) && hasDefaultValue) {\n                usedDefault = true;\n                res = defaultValue;\n            }\n            if (!this.isValidLookup(res)) {\n                usedKey = true;\n                res = key;\n            }\n            const missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n            const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n            const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n            if (usedKey || usedDefault || updateMissing) {\n                this.logger.log(updateMissing ? \"updateKey\" : \"missingKey\", lng, namespace, key, updateMissing ? defaultValue : res);\n                if (keySeparator) {\n                    const fk = this.resolve(key, {\n                        ...options,\n                        keySeparator: false\n                    });\n                    if (fk && fk.res) this.logger.warn(\"Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.\");\n                }\n                let lngs = [];\n                const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n                if (this.options.saveMissingTo === \"fallback\" && fallbackLngs && fallbackLngs[0]) {\n                    for(let i = 0; i < fallbackLngs.length; i++){\n                        lngs.push(fallbackLngs[i]);\n                    }\n                } else if (this.options.saveMissingTo === \"all\") {\n                    lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n                } else {\n                    lngs.push(options.lng || this.language);\n                }\n                const send = (l, k, specificDefaultValue)=>{\n                    const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n                    if (this.options.missingKeyHandler) {\n                        this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, options);\n                    } else if (this.backendConnector && this.backendConnector.saveMissing) {\n                        this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, options);\n                    }\n                    this.emit(\"missingKey\", l, namespace, k, res);\n                };\n                if (this.options.saveMissing) {\n                    if (this.options.saveMissingPlurals && needsPluralHandling) {\n                        lngs.forEach((language)=>{\n                            const suffixes = this.pluralResolver.getSuffixes(language, options);\n                            if (needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                                suffixes.push(`${this.options.pluralSeparator}zero`);\n                            }\n                            suffixes.forEach((suffix)=>{\n                                send([\n                                    language\n                                ], key + suffix, options[`defaultValue${suffix}`] || defaultValue);\n                            });\n                        });\n                    } else {\n                        send(lngs, key, defaultValue);\n                    }\n                }\n            }\n            res = this.extendTranslation(res, keys, options, resolved, lastKey);\n            if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = `${namespace}:${key}`;\n            if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n                if (this.options.compatibilityAPI !== \"v1\") {\n                    res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}:${key}` : key, usedDefault ? res : undefined);\n                } else {\n                    res = this.options.parseMissingKeyHandler(res);\n                }\n            }\n        }\n        if (returnDetails) {\n            resolved.res = res;\n            resolved.usedParams = this.getUsedParamsDetails(options);\n            return resolved;\n        }\n        return res;\n    }\n    extendTranslation(res, key, options, resolved, lastKey) {\n        var _this = this;\n        if (this.i18nFormat && this.i18nFormat.parse) {\n            res = this.i18nFormat.parse(res, {\n                ...this.options.interpolation.defaultVariables,\n                ...options\n            }, options.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n                resolved\n            });\n        } else if (!options.skipInterpolation) {\n            if (options.interpolation) this.interpolator.init({\n                ...options,\n                ...{\n                    interpolation: {\n                        ...this.options.interpolation,\n                        ...options.interpolation\n                    }\n                }\n            });\n            const skipOnVariables = isString(res) && (options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n            let nestBef;\n            if (skipOnVariables) {\n                const nb = res.match(this.interpolator.nestingRegexp);\n                nestBef = nb && nb.length;\n            }\n            let data = options.replace && !isString(options.replace) ? options.replace : options;\n            if (this.options.interpolation.defaultVariables) data = {\n                ...this.options.interpolation.defaultVariables,\n                ...data\n            };\n            res = this.interpolator.interpolate(res, data, options.lng || this.language || resolved.usedLng, options);\n            if (skipOnVariables) {\n                const na = res.match(this.interpolator.nestingRegexp);\n                const nestAft = na && na.length;\n                if (nestBef < nestAft) options.nest = false;\n            }\n            if (!options.lng && this.options.compatibilityAPI !== \"v1\" && resolved && resolved.res) options.lng = this.language || resolved.usedLng;\n            if (options.nest !== false) res = this.interpolator.nest(res, function() {\n                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                    args[_key] = arguments[_key];\n                }\n                if (lastKey && lastKey[0] === args[0] && !options.context) {\n                    _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n                    return null;\n                }\n                return _this.translate(...args, key);\n            }, options);\n            if (options.interpolation) this.interpolator.reset();\n        }\n        const postProcess = options.postProcess || this.options.postProcess;\n        const postProcessorNames = isString(postProcess) ? [\n            postProcess\n        ] : postProcess;\n        if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n            res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n                i18nResolved: {\n                    ...resolved,\n                    usedParams: this.getUsedParamsDetails(options)\n                },\n                ...options\n            } : options, this);\n        }\n        return res;\n    }\n    resolve(keys) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        let found;\n        let usedKey;\n        let exactUsedKey;\n        let usedLng;\n        let usedNS;\n        if (isString(keys)) keys = [\n            keys\n        ];\n        keys.forEach((k)=>{\n            if (this.isValidLookup(found)) return;\n            const extracted = this.extractFromKey(k, options);\n            const key = extracted.key;\n            usedKey = key;\n            let namespaces = extracted.namespaces;\n            if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n            const needsPluralHandling = options.count !== undefined && !isString(options.count);\n            const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n            const needsContextHandling = options.context !== undefined && (isString(options.context) || typeof options.context === \"number\") && options.context !== \"\";\n            const codes = options.lngs ? options.lngs : this.languageUtils.toResolveHierarchy(options.lng || this.language, options.fallbackLng);\n            namespaces.forEach((ns)=>{\n                if (this.isValidLookup(found)) return;\n                usedNS = ns;\n                if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils && this.utils.hasLoadedNamespace && !this.utils.hasLoadedNamespace(usedNS)) {\n                    checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n                    this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(\", \")}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, \"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!\");\n                }\n                codes.forEach((code)=>{\n                    if (this.isValidLookup(found)) return;\n                    usedLng = code;\n                    const finalKeys = [\n                        key\n                    ];\n                    if (this.i18nFormat && this.i18nFormat.addLookupKeys) {\n                        this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n                    } else {\n                        let pluralSuffix;\n                        if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, options.count, options);\n                        const zeroSuffix = `${this.options.pluralSeparator}zero`;\n                        const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n                        if (needsPluralHandling) {\n                            finalKeys.push(key + pluralSuffix);\n                            if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                            }\n                            if (needsZeroSuffixLookup) {\n                                finalKeys.push(key + zeroSuffix);\n                            }\n                        }\n                        if (needsContextHandling) {\n                            const contextKey = `${key}${this.options.contextSeparator}${options.context}`;\n                            finalKeys.push(contextKey);\n                            if (needsPluralHandling) {\n                                finalKeys.push(contextKey + pluralSuffix);\n                                if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                                    finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                                }\n                                if (needsZeroSuffixLookup) {\n                                    finalKeys.push(contextKey + zeroSuffix);\n                                }\n                            }\n                        }\n                    }\n                    let possibleKey;\n                    while(possibleKey = finalKeys.pop()){\n                        if (!this.isValidLookup(found)) {\n                            exactUsedKey = possibleKey;\n                            found = this.getResource(code, ns, possibleKey, options);\n                        }\n                    }\n                });\n            });\n        });\n        return {\n            res: found,\n            usedKey,\n            exactUsedKey,\n            usedLng,\n            usedNS\n        };\n    }\n    isValidLookup(res) {\n        return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === \"\");\n    }\n    getResource(code, ns, key) {\n        let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n        if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n        return this.resourceStore.getResource(code, ns, key, options);\n    }\n    getUsedParamsDetails() {\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        const optionsKeys = [\n            \"defaultValue\",\n            \"ordinal\",\n            \"context\",\n            \"replace\",\n            \"lng\",\n            \"lngs\",\n            \"fallbackLng\",\n            \"ns\",\n            \"keySeparator\",\n            \"nsSeparator\",\n            \"returnObjects\",\n            \"returnDetails\",\n            \"joinArrays\",\n            \"postProcess\",\n            \"interpolation\"\n        ];\n        const useOptionsReplaceForData = options.replace && !isString(options.replace);\n        let data = useOptionsReplaceForData ? options.replace : options;\n        if (useOptionsReplaceForData && typeof options.count !== \"undefined\") {\n            data.count = options.count;\n        }\n        if (this.options.interpolation.defaultVariables) {\n            data = {\n                ...this.options.interpolation.defaultVariables,\n                ...data\n            };\n        }\n        if (!useOptionsReplaceForData) {\n            data = {\n                ...data\n            };\n            for (const key of optionsKeys){\n                delete data[key];\n            }\n        }\n        return data;\n    }\n    static hasDefaultValue(options) {\n        const prefix = \"defaultValue\";\n        for(const option in options){\n            if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\nconst capitalize = (string)=>string.charAt(0).toUpperCase() + string.slice(1);\nclass LanguageUtil {\n    constructor(options){\n        this.options = options;\n        this.supportedLngs = this.options.supportedLngs || false;\n        this.logger = baseLogger.create(\"languageUtils\");\n    }\n    getScriptPartFromCode(code) {\n        code = getCleanedCode(code);\n        if (!code || code.indexOf(\"-\") < 0) return null;\n        const p = code.split(\"-\");\n        if (p.length === 2) return null;\n        p.pop();\n        if (p[p.length - 1].toLowerCase() === \"x\") return null;\n        return this.formatLanguageCode(p.join(\"-\"));\n    }\n    getLanguagePartFromCode(code) {\n        code = getCleanedCode(code);\n        if (!code || code.indexOf(\"-\") < 0) return code;\n        const p = code.split(\"-\");\n        return this.formatLanguageCode(p[0]);\n    }\n    formatLanguageCode(code) {\n        if (isString(code) && code.indexOf(\"-\") > -1) {\n            if (typeof Intl !== \"undefined\" && typeof Intl.getCanonicalLocales !== \"undefined\") {\n                try {\n                    let formattedCode = Intl.getCanonicalLocales(code)[0];\n                    if (formattedCode && this.options.lowerCaseLng) {\n                        formattedCode = formattedCode.toLowerCase();\n                    }\n                    if (formattedCode) return formattedCode;\n                } catch (e) {}\n            }\n            const specialCases = [\n                \"hans\",\n                \"hant\",\n                \"latn\",\n                \"cyrl\",\n                \"cans\",\n                \"mong\",\n                \"arab\"\n            ];\n            let p = code.split(\"-\");\n            if (this.options.lowerCaseLng) {\n                p = p.map((part)=>part.toLowerCase());\n            } else if (p.length === 2) {\n                p[0] = p[0].toLowerCase();\n                p[1] = p[1].toUpperCase();\n                if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n            } else if (p.length === 3) {\n                p[0] = p[0].toLowerCase();\n                if (p[1].length === 2) p[1] = p[1].toUpperCase();\n                if (p[0] !== \"sgn\" && p[2].length === 2) p[2] = p[2].toUpperCase();\n                if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n                if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n            }\n            return p.join(\"-\");\n        }\n        return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n    }\n    isSupportedCode(code) {\n        if (this.options.load === \"languageOnly\" || this.options.nonExplicitSupportedLngs) {\n            code = this.getLanguagePartFromCode(code);\n        }\n        return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n    }\n    getBestMatchFromCodes(codes) {\n        if (!codes) return null;\n        let found;\n        codes.forEach((code)=>{\n            if (found) return;\n            const cleanedLng = this.formatLanguageCode(code);\n            if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n        });\n        if (!found && this.options.supportedLngs) {\n            codes.forEach((code)=>{\n                if (found) return;\n                const lngOnly = this.getLanguagePartFromCode(code);\n                if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n                found = this.options.supportedLngs.find((supportedLng)=>{\n                    if (supportedLng === lngOnly) return supportedLng;\n                    if (supportedLng.indexOf(\"-\") < 0 && lngOnly.indexOf(\"-\") < 0) return;\n                    if (supportedLng.indexOf(\"-\") > 0 && lngOnly.indexOf(\"-\") < 0 && supportedLng.substring(0, supportedLng.indexOf(\"-\")) === lngOnly) return supportedLng;\n                    if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n                });\n            });\n        }\n        if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n        return found;\n    }\n    getFallbackCodes(fallbacks, code) {\n        if (!fallbacks) return [];\n        if (typeof fallbacks === \"function\") fallbacks = fallbacks(code);\n        if (isString(fallbacks)) fallbacks = [\n            fallbacks\n        ];\n        if (Array.isArray(fallbacks)) return fallbacks;\n        if (!code) return fallbacks.default || [];\n        let found = fallbacks[code];\n        if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n        if (!found) found = fallbacks[this.formatLanguageCode(code)];\n        if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n        if (!found) found = fallbacks.default;\n        return found || [];\n    }\n    toResolveHierarchy(code, fallbackCode) {\n        const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n        const codes = [];\n        const addCode = (c)=>{\n            if (!c) return;\n            if (this.isSupportedCode(c)) {\n                codes.push(c);\n            } else {\n                this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n            }\n        };\n        if (isString(code) && (code.indexOf(\"-\") > -1 || code.indexOf(\"_\") > -1)) {\n            if (this.options.load !== \"languageOnly\") addCode(this.formatLanguageCode(code));\n            if (this.options.load !== \"languageOnly\" && this.options.load !== \"currentOnly\") addCode(this.getScriptPartFromCode(code));\n            if (this.options.load !== \"currentOnly\") addCode(this.getLanguagePartFromCode(code));\n        } else if (isString(code)) {\n            addCode(this.formatLanguageCode(code));\n        }\n        fallbackCodes.forEach((fc)=>{\n            if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n        });\n        return codes;\n    }\n}\nlet sets = [\n    {\n        lngs: [\n            \"ach\",\n            \"ak\",\n            \"am\",\n            \"arn\",\n            \"br\",\n            \"fil\",\n            \"gun\",\n            \"ln\",\n            \"mfe\",\n            \"mg\",\n            \"mi\",\n            \"oc\",\n            \"pt\",\n            \"pt-BR\",\n            \"tg\",\n            \"tl\",\n            \"ti\",\n            \"tr\",\n            \"uz\",\n            \"wa\"\n        ],\n        nr: [\n            1,\n            2\n        ],\n        fc: 1\n    },\n    {\n        lngs: [\n            \"af\",\n            \"an\",\n            \"ast\",\n            \"az\",\n            \"bg\",\n            \"bn\",\n            \"ca\",\n            \"da\",\n            \"de\",\n            \"dev\",\n            \"el\",\n            \"en\",\n            \"eo\",\n            \"es\",\n            \"et\",\n            \"eu\",\n            \"fi\",\n            \"fo\",\n            \"fur\",\n            \"fy\",\n            \"gl\",\n            \"gu\",\n            \"ha\",\n            \"hi\",\n            \"hu\",\n            \"hy\",\n            \"ia\",\n            \"it\",\n            \"kk\",\n            \"kn\",\n            \"ku\",\n            \"lb\",\n            \"mai\",\n            \"ml\",\n            \"mn\",\n            \"mr\",\n            \"nah\",\n            \"nap\",\n            \"nb\",\n            \"ne\",\n            \"nl\",\n            \"nn\",\n            \"no\",\n            \"nso\",\n            \"pa\",\n            \"pap\",\n            \"pms\",\n            \"ps\",\n            \"pt-PT\",\n            \"rm\",\n            \"sco\",\n            \"se\",\n            \"si\",\n            \"so\",\n            \"son\",\n            \"sq\",\n            \"sv\",\n            \"sw\",\n            \"ta\",\n            \"te\",\n            \"tk\",\n            \"ur\",\n            \"yo\"\n        ],\n        nr: [\n            1,\n            2\n        ],\n        fc: 2\n    },\n    {\n        lngs: [\n            \"ay\",\n            \"bo\",\n            \"cgg\",\n            \"fa\",\n            \"ht\",\n            \"id\",\n            \"ja\",\n            \"jbo\",\n            \"ka\",\n            \"km\",\n            \"ko\",\n            \"ky\",\n            \"lo\",\n            \"ms\",\n            \"sah\",\n            \"su\",\n            \"th\",\n            \"tt\",\n            \"ug\",\n            \"vi\",\n            \"wo\",\n            \"zh\"\n        ],\n        nr: [\n            1\n        ],\n        fc: 3\n    },\n    {\n        lngs: [\n            \"be\",\n            \"bs\",\n            \"cnr\",\n            \"dz\",\n            \"hr\",\n            \"ru\",\n            \"sr\",\n            \"uk\"\n        ],\n        nr: [\n            1,\n            2,\n            5\n        ],\n        fc: 4\n    },\n    {\n        lngs: [\n            \"ar\"\n        ],\n        nr: [\n            0,\n            1,\n            2,\n            3,\n            11,\n            100\n        ],\n        fc: 5\n    },\n    {\n        lngs: [\n            \"cs\",\n            \"sk\"\n        ],\n        nr: [\n            1,\n            2,\n            5\n        ],\n        fc: 6\n    },\n    {\n        lngs: [\n            \"csb\",\n            \"pl\"\n        ],\n        nr: [\n            1,\n            2,\n            5\n        ],\n        fc: 7\n    },\n    {\n        lngs: [\n            \"cy\"\n        ],\n        nr: [\n            1,\n            2,\n            3,\n            8\n        ],\n        fc: 8\n    },\n    {\n        lngs: [\n            \"fr\"\n        ],\n        nr: [\n            1,\n            2\n        ],\n        fc: 9\n    },\n    {\n        lngs: [\n            \"ga\"\n        ],\n        nr: [\n            1,\n            2,\n            3,\n            7,\n            11\n        ],\n        fc: 10\n    },\n    {\n        lngs: [\n            \"gd\"\n        ],\n        nr: [\n            1,\n            2,\n            3,\n            20\n        ],\n        fc: 11\n    },\n    {\n        lngs: [\n            \"is\"\n        ],\n        nr: [\n            1,\n            2\n        ],\n        fc: 12\n    },\n    {\n        lngs: [\n            \"jv\"\n        ],\n        nr: [\n            0,\n            1\n        ],\n        fc: 13\n    },\n    {\n        lngs: [\n            \"kw\"\n        ],\n        nr: [\n            1,\n            2,\n            3,\n            4\n        ],\n        fc: 14\n    },\n    {\n        lngs: [\n            \"lt\"\n        ],\n        nr: [\n            1,\n            2,\n            10\n        ],\n        fc: 15\n    },\n    {\n        lngs: [\n            \"lv\"\n        ],\n        nr: [\n            1,\n            2,\n            0\n        ],\n        fc: 16\n    },\n    {\n        lngs: [\n            \"mk\"\n        ],\n        nr: [\n            1,\n            2\n        ],\n        fc: 17\n    },\n    {\n        lngs: [\n            \"mnk\"\n        ],\n        nr: [\n            0,\n            1,\n            2\n        ],\n        fc: 18\n    },\n    {\n        lngs: [\n            \"mt\"\n        ],\n        nr: [\n            1,\n            2,\n            11,\n            20\n        ],\n        fc: 19\n    },\n    {\n        lngs: [\n            \"or\"\n        ],\n        nr: [\n            2,\n            1\n        ],\n        fc: 2\n    },\n    {\n        lngs: [\n            \"ro\"\n        ],\n        nr: [\n            1,\n            2,\n            20\n        ],\n        fc: 20\n    },\n    {\n        lngs: [\n            \"sl\"\n        ],\n        nr: [\n            5,\n            1,\n            2,\n            3\n        ],\n        fc: 21\n    },\n    {\n        lngs: [\n            \"he\",\n            \"iw\"\n        ],\n        nr: [\n            1,\n            2,\n            20,\n            21\n        ],\n        fc: 22\n    }\n];\nlet _rulesPluralsTypes = {\n    1: (n)=>Number(n > 1),\n    2: (n)=>Number(n != 1),\n    3: (n)=>0,\n    4: (n)=>Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n    5: (n)=>Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5),\n    6: (n)=>Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2),\n    7: (n)=>Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n    8: (n)=>Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3),\n    9: (n)=>Number(n >= 2),\n    10: (n)=>Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4),\n    11: (n)=>Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3),\n    12: (n)=>Number(n % 10 != 1 || n % 100 == 11),\n    13: (n)=>Number(n !== 0),\n    14: (n)=>Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3),\n    15: (n)=>Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n    16: (n)=>Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2),\n    17: (n)=>Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1),\n    18: (n)=>Number(n == 0 ? 0 : n == 1 ? 1 : 2),\n    19: (n)=>Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3),\n    20: (n)=>Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2),\n    21: (n)=>Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0),\n    22: (n)=>Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3)\n};\nconst nonIntlVersions = [\n    \"v1\",\n    \"v2\",\n    \"v3\"\n];\nconst intlVersions = [\n    \"v4\"\n];\nconst suffixesOrder = {\n    zero: 0,\n    one: 1,\n    two: 2,\n    few: 3,\n    many: 4,\n    other: 5\n};\nconst createRules = ()=>{\n    const rules = {};\n    sets.forEach((set)=>{\n        set.lngs.forEach((l)=>{\n            rules[l] = {\n                numbers: set.nr,\n                plurals: _rulesPluralsTypes[set.fc]\n            };\n        });\n    });\n    return rules;\n};\nclass PluralResolver {\n    constructor(languageUtils){\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        this.languageUtils = languageUtils;\n        this.options = options;\n        this.logger = baseLogger.create(\"pluralResolver\");\n        if ((!this.options.compatibilityJSON || intlVersions.includes(this.options.compatibilityJSON)) && (typeof Intl === \"undefined\" || !Intl.PluralRules)) {\n            this.options.compatibilityJSON = \"v3\";\n            this.logger.error(\"Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.\");\n        }\n        this.rules = createRules();\n        this.pluralRulesCache = {};\n    }\n    addRule(lng, obj) {\n        this.rules[lng] = obj;\n    }\n    clearCache() {\n        this.pluralRulesCache = {};\n    }\n    getRule(code) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        if (this.shouldUseIntlApi()) {\n            const cleanedCode = getCleanedCode(code === \"dev\" ? \"en\" : code);\n            const type = options.ordinal ? \"ordinal\" : \"cardinal\";\n            const cacheKey = JSON.stringify({\n                cleanedCode,\n                type\n            });\n            if (cacheKey in this.pluralRulesCache) {\n                return this.pluralRulesCache[cacheKey];\n            }\n            let rule;\n            try {\n                rule = new Intl.PluralRules(cleanedCode, {\n                    type\n                });\n            } catch (err) {\n                if (!code.match(/-|_/)) return;\n                const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n                rule = this.getRule(lngPart, options);\n            }\n            this.pluralRulesCache[cacheKey] = rule;\n            return rule;\n        }\n        return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n    }\n    needsPlural(code) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const rule = this.getRule(code, options);\n        if (this.shouldUseIntlApi()) {\n            return rule && rule.resolvedOptions().pluralCategories.length > 1;\n        }\n        return rule && rule.numbers.length > 1;\n    }\n    getPluralFormsOfKey(code, key) {\n        let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        return this.getSuffixes(code, options).map((suffix)=>`${key}${suffix}`);\n    }\n    getSuffixes(code) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const rule = this.getRule(code, options);\n        if (!rule) {\n            return [];\n        }\n        if (this.shouldUseIntlApi()) {\n            return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2)=>suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map((pluralCategory)=>`${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : \"\"}${pluralCategory}`);\n        }\n        return rule.numbers.map((number)=>this.getSuffix(code, number, options));\n    }\n    getSuffix(code, count) {\n        let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        const rule = this.getRule(code, options);\n        if (rule) {\n            if (this.shouldUseIntlApi()) {\n                return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : \"\"}${rule.select(count)}`;\n            }\n            return this.getSuffixRetroCompatible(rule, count);\n        }\n        this.logger.warn(`no plural rule found for: ${code}`);\n        return \"\";\n    }\n    getSuffixRetroCompatible(rule, count) {\n        const idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n        let suffix = rule.numbers[idx];\n        if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n            if (suffix === 2) {\n                suffix = \"plural\";\n            } else if (suffix === 1) {\n                suffix = \"\";\n            }\n        }\n        const returnSuffix = ()=>this.options.prepend && suffix.toString() ? this.options.prepend + suffix.toString() : suffix.toString();\n        if (this.options.compatibilityJSON === \"v1\") {\n            if (suffix === 1) return \"\";\n            if (typeof suffix === \"number\") return `_plural_${suffix.toString()}`;\n            return returnSuffix();\n        } else if (this.options.compatibilityJSON === \"v2\") {\n            return returnSuffix();\n        } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n            return returnSuffix();\n        }\n        return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n    }\n    shouldUseIntlApi() {\n        return !nonIntlVersions.includes(this.options.compatibilityJSON);\n    }\n}\nconst deepFindWithDefaults = function(data, defaultData, key) {\n    let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : \".\";\n    let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    let path = getPathWithDefaults(data, defaultData, key);\n    if (!path && ignoreJSONStructure && isString(key)) {\n        path = deepFind(data, key, keySeparator);\n        if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n    }\n    return path;\n};\nconst regexSafe = (val)=>val.replace(/\\$/g, \"$$$$\");\nclass Interpolator {\n    constructor(){\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        this.logger = baseLogger.create(\"interpolator\");\n        this.options = options;\n        this.format = options.interpolation && options.interpolation.format || ((value)=>value);\n        this.init(options);\n    }\n    init() {\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        if (!options.interpolation) options.interpolation = {\n            escapeValue: true\n        };\n        const { escape: escape$1, escapeValue, useRawValueToEscape, prefix, prefixEscaped, suffix, suffixEscaped, formatSeparator, unescapeSuffix, unescapePrefix, nestingPrefix, nestingPrefixEscaped, nestingSuffix, nestingSuffixEscaped, nestingOptionsSeparator, maxReplaces, alwaysFormat } = options.interpolation;\n        this.escape = escape$1 !== undefined ? escape$1 : escape;\n        this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n        this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n        this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || \"{{\";\n        this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || \"}}\";\n        this.formatSeparator = formatSeparator || \",\";\n        this.unescapePrefix = unescapeSuffix ? \"\" : unescapePrefix || \"-\";\n        this.unescapeSuffix = this.unescapePrefix ? \"\" : unescapeSuffix || \"\";\n        this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape(\"$t(\");\n        this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(\")\");\n        this.nestingOptionsSeparator = nestingOptionsSeparator || \",\";\n        this.maxReplaces = maxReplaces || 1000;\n        this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n        this.resetRegExp();\n    }\n    reset() {\n        if (this.options) this.init(this.options);\n    }\n    resetRegExp() {\n        const getOrResetRegExp = (existingRegExp, pattern)=>{\n            if (existingRegExp && existingRegExp.source === pattern) {\n                existingRegExp.lastIndex = 0;\n                return existingRegExp;\n            }\n            return new RegExp(pattern, \"g\");\n        };\n        this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n        this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n        this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n    }\n    interpolate(str, data, lng, options) {\n        let match;\n        let value;\n        let replaces;\n        const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n        const handleFormat = (key)=>{\n            if (key.indexOf(this.formatSeparator) < 0) {\n                const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n                return this.alwaysFormat ? this.format(path, undefined, lng, {\n                    ...options,\n                    ...data,\n                    interpolationkey: key\n                }) : path;\n            }\n            const p = key.split(this.formatSeparator);\n            const k = p.shift().trim();\n            const f = p.join(this.formatSeparator).trim();\n            return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n                ...options,\n                ...data,\n                interpolationkey: k\n            });\n        };\n        this.resetRegExp();\n        const missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n        const skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n        const todos = [\n            {\n                regex: this.regexpUnescape,\n                safeValue: (val)=>regexSafe(val)\n            },\n            {\n                regex: this.regexp,\n                safeValue: (val)=>this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n            }\n        ];\n        todos.forEach((todo)=>{\n            replaces = 0;\n            while(match = todo.regex.exec(str)){\n                const matchedVar = match[1].trim();\n                value = handleFormat(matchedVar);\n                if (value === undefined) {\n                    if (typeof missingInterpolationHandler === \"function\") {\n                        const temp = missingInterpolationHandler(str, match, options);\n                        value = isString(temp) ? temp : \"\";\n                    } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n                        value = \"\";\n                    } else if (skipOnVariables) {\n                        value = match[0];\n                        continue;\n                    } else {\n                        this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n                        value = \"\";\n                    }\n                } else if (!isString(value) && !this.useRawValueToEscape) {\n                    value = makeString(value);\n                }\n                const safeValue = todo.safeValue(value);\n                str = str.replace(match[0], safeValue);\n                if (skipOnVariables) {\n                    todo.regex.lastIndex += value.length;\n                    todo.regex.lastIndex -= match[0].length;\n                } else {\n                    todo.regex.lastIndex = 0;\n                }\n                replaces++;\n                if (replaces >= this.maxReplaces) {\n                    break;\n                }\n            }\n        });\n        return str;\n    }\n    nest(str, fc) {\n        let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        let match;\n        let value;\n        let clonedOptions;\n        const handleHasOptions = (key, inheritedOptions)=>{\n            const sep = this.nestingOptionsSeparator;\n            if (key.indexOf(sep) < 0) return key;\n            const c = key.split(new RegExp(`${sep}[ ]*{`));\n            let optionsString = `{${c[1]}`;\n            key = c[0];\n            optionsString = this.interpolate(optionsString, clonedOptions);\n            const matchedSingleQuotes = optionsString.match(/'/g);\n            const matchedDoubleQuotes = optionsString.match(/\"/g);\n            if (matchedSingleQuotes && matchedSingleQuotes.length % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n                optionsString = optionsString.replace(/'/g, '\"');\n            }\n            try {\n                clonedOptions = JSON.parse(optionsString);\n                if (inheritedOptions) clonedOptions = {\n                    ...inheritedOptions,\n                    ...clonedOptions\n                };\n            } catch (e) {\n                this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n                return `${key}${sep}${optionsString}`;\n            }\n            if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n            return key;\n        };\n        while(match = this.nestingRegexp.exec(str)){\n            let formatters = [];\n            clonedOptions = {\n                ...options\n            };\n            clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n            clonedOptions.applyPostProcessor = false;\n            delete clonedOptions.defaultValue;\n            let doReduce = false;\n            if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n                const r = match[1].split(this.formatSeparator).map((elem)=>elem.trim());\n                match[1] = r.shift();\n                formatters = r;\n                doReduce = true;\n            }\n            value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n            if (value && match[0] === str && !isString(value)) return value;\n            if (!isString(value)) value = makeString(value);\n            if (!value) {\n                this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n                value = \"\";\n            }\n            if (doReduce) {\n                value = formatters.reduce((v, f)=>this.format(v, f, options.lng, {\n                        ...options,\n                        interpolationkey: match[1].trim()\n                    }), value.trim());\n            }\n            str = str.replace(match[0], value);\n            this.regexp.lastIndex = 0;\n        }\n        return str;\n    }\n}\nconst parseFormatStr = (formatStr)=>{\n    let formatName = formatStr.toLowerCase().trim();\n    const formatOptions = {};\n    if (formatStr.indexOf(\"(\") > -1) {\n        const p = formatStr.split(\"(\");\n        formatName = p[0].toLowerCase().trim();\n        const optStr = p[1].substring(0, p[1].length - 1);\n        if (formatName === \"currency\" && optStr.indexOf(\":\") < 0) {\n            if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n        } else if (formatName === \"relativetime\" && optStr.indexOf(\":\") < 0) {\n            if (!formatOptions.range) formatOptions.range = optStr.trim();\n        } else {\n            const opts = optStr.split(\";\");\n            opts.forEach((opt)=>{\n                if (opt) {\n                    const [key, ...rest] = opt.split(\":\");\n                    const val = rest.join(\":\").trim().replace(/^'+|'+$/g, \"\");\n                    const trimmedKey = key.trim();\n                    if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n                    if (val === \"false\") formatOptions[trimmedKey] = false;\n                    if (val === \"true\") formatOptions[trimmedKey] = true;\n                    if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n                }\n            });\n        }\n    }\n    return {\n        formatName,\n        formatOptions\n    };\n};\nconst createCachedFormatter = (fn)=>{\n    const cache = {};\n    return (val, lng, options)=>{\n        let optForCache = options;\n        if (options && options.interpolationkey && options.formatParams && options.formatParams[options.interpolationkey] && options[options.interpolationkey]) {\n            optForCache = {\n                ...optForCache,\n                [options.interpolationkey]: undefined\n            };\n        }\n        const key = lng + JSON.stringify(optForCache);\n        let formatter = cache[key];\n        if (!formatter) {\n            formatter = fn(getCleanedCode(lng), options);\n            cache[key] = formatter;\n        }\n        return formatter(val);\n    };\n};\nclass Formatter {\n    constructor(){\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        this.logger = baseLogger.create(\"formatter\");\n        this.options = options;\n        this.formats = {\n            number: createCachedFormatter((lng, opt)=>{\n                const formatter = new Intl.NumberFormat(lng, {\n                    ...opt\n                });\n                return (val)=>formatter.format(val);\n            }),\n            currency: createCachedFormatter((lng, opt)=>{\n                const formatter = new Intl.NumberFormat(lng, {\n                    ...opt,\n                    style: \"currency\"\n                });\n                return (val)=>formatter.format(val);\n            }),\n            datetime: createCachedFormatter((lng, opt)=>{\n                const formatter = new Intl.DateTimeFormat(lng, {\n                    ...opt\n                });\n                return (val)=>formatter.format(val);\n            }),\n            relativetime: createCachedFormatter((lng, opt)=>{\n                const formatter = new Intl.RelativeTimeFormat(lng, {\n                    ...opt\n                });\n                return (val)=>formatter.format(val, opt.range || \"day\");\n            }),\n            list: createCachedFormatter((lng, opt)=>{\n                const formatter = new Intl.ListFormat(lng, {\n                    ...opt\n                });\n                return (val)=>formatter.format(val);\n            })\n        };\n        this.init(options);\n    }\n    init(services) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n            interpolation: {}\n        };\n        this.formatSeparator = options.interpolation.formatSeparator || \",\";\n    }\n    add(name, fc) {\n        this.formats[name.toLowerCase().trim()] = fc;\n    }\n    addCached(name, fc) {\n        this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n    }\n    format(value, format, lng) {\n        let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n        const formats = format.split(this.formatSeparator);\n        if (formats.length > 1 && formats[0].indexOf(\"(\") > 1 && formats[0].indexOf(\")\") < 0 && formats.find((f)=>f.indexOf(\")\") > -1)) {\n            const lastIndex = formats.findIndex((f)=>f.indexOf(\")\") > -1);\n            formats[0] = [\n                formats[0],\n                ...formats.splice(1, lastIndex)\n            ].join(this.formatSeparator);\n        }\n        const result = formats.reduce((mem, f)=>{\n            const { formatName, formatOptions } = parseFormatStr(f);\n            if (this.formats[formatName]) {\n                let formatted = mem;\n                try {\n                    const valOptions = options && options.formatParams && options.formatParams[options.interpolationkey] || {};\n                    const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n                    formatted = this.formats[formatName](mem, l, {\n                        ...formatOptions,\n                        ...options,\n                        ...valOptions\n                    });\n                } catch (error) {\n                    this.logger.warn(error);\n                }\n                return formatted;\n            } else {\n                this.logger.warn(`there was no format function for ${formatName}`);\n            }\n            return mem;\n        }, value);\n        return result;\n    }\n}\nconst removePending = (q, name)=>{\n    if (q.pending[name] !== undefined) {\n        delete q.pending[name];\n        q.pendingCount--;\n    }\n};\nclass Connector extends EventEmitter {\n    constructor(backend, store, services){\n        let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n        super();\n        this.backend = backend;\n        this.store = store;\n        this.services = services;\n        this.languageUtils = services.languageUtils;\n        this.options = options;\n        this.logger = baseLogger.create(\"backendConnector\");\n        this.waitingReads = [];\n        this.maxParallelReads = options.maxParallelReads || 10;\n        this.readingCalls = 0;\n        this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n        this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n        this.state = {};\n        this.queue = [];\n        if (this.backend && this.backend.init) {\n            this.backend.init(services, options.backend, options);\n        }\n    }\n    queueLoad(languages, namespaces, options, callback) {\n        const toLoad = {};\n        const pending = {};\n        const toLoadLanguages = {};\n        const toLoadNamespaces = {};\n        languages.forEach((lng)=>{\n            let hasAllNamespaces = true;\n            namespaces.forEach((ns)=>{\n                const name = `${lng}|${ns}`;\n                if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n                    this.state[name] = 2;\n                } else if (this.state[name] < 0) ;\n                else if (this.state[name] === 1) {\n                    if (pending[name] === undefined) pending[name] = true;\n                } else {\n                    this.state[name] = 1;\n                    hasAllNamespaces = false;\n                    if (pending[name] === undefined) pending[name] = true;\n                    if (toLoad[name] === undefined) toLoad[name] = true;\n                    if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n                }\n            });\n            if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n        });\n        if (Object.keys(toLoad).length || Object.keys(pending).length) {\n            this.queue.push({\n                pending,\n                pendingCount: Object.keys(pending).length,\n                loaded: {},\n                errors: [],\n                callback\n            });\n        }\n        return {\n            toLoad: Object.keys(toLoad),\n            pending: Object.keys(pending),\n            toLoadLanguages: Object.keys(toLoadLanguages),\n            toLoadNamespaces: Object.keys(toLoadNamespaces)\n        };\n    }\n    loaded(name, err, data) {\n        const s = name.split(\"|\");\n        const lng = s[0];\n        const ns = s[1];\n        if (err) this.emit(\"failedLoading\", lng, ns, err);\n        if (!err && data) {\n            this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n                skipCopy: true\n            });\n        }\n        this.state[name] = err ? -1 : 2;\n        if (err && data) this.state[name] = 0;\n        const loaded = {};\n        this.queue.forEach((q)=>{\n            pushPath(q.loaded, [\n                lng\n            ], ns);\n            removePending(q, name);\n            if (err) q.errors.push(err);\n            if (q.pendingCount === 0 && !q.done) {\n                Object.keys(q.loaded).forEach((l)=>{\n                    if (!loaded[l]) loaded[l] = {};\n                    const loadedKeys = q.loaded[l];\n                    if (loadedKeys.length) {\n                        loadedKeys.forEach((n)=>{\n                            if (loaded[l][n] === undefined) loaded[l][n] = true;\n                        });\n                    }\n                });\n                q.done = true;\n                if (q.errors.length) {\n                    q.callback(q.errors);\n                } else {\n                    q.callback();\n                }\n            }\n        });\n        this.emit(\"loaded\", loaded);\n        this.queue = this.queue.filter((q)=>!q.done);\n    }\n    read(lng, ns, fcName) {\n        let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n        let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n        let callback = arguments.length > 5 ? arguments[5] : undefined;\n        if (!lng.length) return callback(null, {});\n        if (this.readingCalls >= this.maxParallelReads) {\n            this.waitingReads.push({\n                lng,\n                ns,\n                fcName,\n                tried,\n                wait,\n                callback\n            });\n            return;\n        }\n        this.readingCalls++;\n        const resolver = (err, data)=>{\n            this.readingCalls--;\n            if (this.waitingReads.length > 0) {\n                const next = this.waitingReads.shift();\n                this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n            }\n            if (err && data && tried < this.maxRetries) {\n                setTimeout(()=>{\n                    this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n                }, wait);\n                return;\n            }\n            callback(err, data);\n        };\n        const fc = this.backend[fcName].bind(this.backend);\n        if (fc.length === 2) {\n            try {\n                const r = fc(lng, ns);\n                if (r && typeof r.then === \"function\") {\n                    r.then((data)=>resolver(null, data)).catch(resolver);\n                } else {\n                    resolver(null, r);\n                }\n            } catch (err) {\n                resolver(err);\n            }\n            return;\n        }\n        return fc(lng, ns, resolver);\n    }\n    prepareLoading(languages, namespaces) {\n        let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n        let callback = arguments.length > 3 ? arguments[3] : undefined;\n        if (!this.backend) {\n            this.logger.warn(\"No backend was added via i18next.use. Will not load resources.\");\n            return callback && callback();\n        }\n        if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n        if (isString(namespaces)) namespaces = [\n            namespaces\n        ];\n        const toLoad = this.queueLoad(languages, namespaces, options, callback);\n        if (!toLoad.toLoad.length) {\n            if (!toLoad.pending.length) callback();\n            return null;\n        }\n        toLoad.toLoad.forEach((name)=>{\n            this.loadOne(name);\n        });\n    }\n    load(languages, namespaces, callback) {\n        this.prepareLoading(languages, namespaces, {}, callback);\n    }\n    reload(languages, namespaces, callback) {\n        this.prepareLoading(languages, namespaces, {\n            reload: true\n        }, callback);\n    }\n    loadOne(name) {\n        let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n        const s = name.split(\"|\");\n        const lng = s[0];\n        const ns = s[1];\n        this.read(lng, ns, \"read\", undefined, undefined, (err, data)=>{\n            if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n            if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n            this.loaded(name, err, data);\n        });\n    }\n    saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n        let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n        let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : ()=>{};\n        if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n            this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, \"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!\");\n            return;\n        }\n        if (key === undefined || key === null || key === \"\") return;\n        if (this.backend && this.backend.create) {\n            const opts = {\n                ...options,\n                isUpdate\n            };\n            const fc = this.backend.create.bind(this.backend);\n            if (fc.length < 6) {\n                try {\n                    let r;\n                    if (fc.length === 5) {\n                        r = fc(languages, namespace, key, fallbackValue, opts);\n                    } else {\n                        r = fc(languages, namespace, key, fallbackValue);\n                    }\n                    if (r && typeof r.then === \"function\") {\n                        r.then((data)=>clb(null, data)).catch(clb);\n                    } else {\n                        clb(null, r);\n                    }\n                } catch (err) {\n                    clb(err);\n                }\n            } else {\n                fc(languages, namespace, key, fallbackValue, clb, opts);\n            }\n        }\n        if (!languages || !languages[0]) return;\n        this.store.addResource(languages[0], namespace, key, fallbackValue);\n    }\n}\nconst get = ()=>({\n        debug: false,\n        initImmediate: true,\n        ns: [\n            \"translation\"\n        ],\n        defaultNS: [\n            \"translation\"\n        ],\n        fallbackLng: [\n            \"dev\"\n        ],\n        fallbackNS: false,\n        supportedLngs: false,\n        nonExplicitSupportedLngs: false,\n        load: \"all\",\n        preload: false,\n        simplifyPluralSuffix: true,\n        keySeparator: \".\",\n        nsSeparator: \":\",\n        pluralSeparator: \"_\",\n        contextSeparator: \"_\",\n        partialBundledLanguages: false,\n        saveMissing: false,\n        updateMissing: false,\n        saveMissingTo: \"fallback\",\n        saveMissingPlurals: true,\n        missingKeyHandler: false,\n        missingInterpolationHandler: false,\n        postProcess: false,\n        postProcessPassResolved: false,\n        returnNull: false,\n        returnEmptyString: true,\n        returnObjects: false,\n        joinArrays: false,\n        returnedObjectHandler: false,\n        parseMissingKeyHandler: false,\n        appendNamespaceToMissingKey: false,\n        appendNamespaceToCIMode: false,\n        overloadTranslationOptionHandler: (args)=>{\n            let ret = {};\n            if (typeof args[1] === \"object\") ret = args[1];\n            if (isString(args[1])) ret.defaultValue = args[1];\n            if (isString(args[2])) ret.tDescription = args[2];\n            if (typeof args[2] === \"object\" || typeof args[3] === \"object\") {\n                const options = args[3] || args[2];\n                Object.keys(options).forEach((key)=>{\n                    ret[key] = options[key];\n                });\n            }\n            return ret;\n        },\n        interpolation: {\n            escapeValue: true,\n            format: (value)=>value,\n            prefix: \"{{\",\n            suffix: \"}}\",\n            formatSeparator: \",\",\n            unescapePrefix: \"-\",\n            nestingPrefix: \"$t(\",\n            nestingSuffix: \")\",\n            nestingOptionsSeparator: \",\",\n            maxReplaces: 1000,\n            skipOnVariables: true\n        }\n    });\nconst transformOptions = (options)=>{\n    if (isString(options.ns)) options.ns = [\n        options.ns\n    ];\n    if (isString(options.fallbackLng)) options.fallbackLng = [\n        options.fallbackLng\n    ];\n    if (isString(options.fallbackNS)) options.fallbackNS = [\n        options.fallbackNS\n    ];\n    if (options.supportedLngs && options.supportedLngs.indexOf(\"cimode\") < 0) {\n        options.supportedLngs = options.supportedLngs.concat([\n            \"cimode\"\n        ]);\n    }\n    return options;\n};\nconst noop = ()=>{};\nconst bindMemberFunctions = (inst)=>{\n    const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n    mems.forEach((mem)=>{\n        if (typeof inst[mem] === \"function\") {\n            inst[mem] = inst[mem].bind(inst);\n        }\n    });\n};\nclass I18n extends EventEmitter {\n    constructor(){\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        let callback = arguments.length > 1 ? arguments[1] : undefined;\n        super();\n        this.options = transformOptions(options);\n        this.services = {};\n        this.logger = baseLogger;\n        this.modules = {\n            external: []\n        };\n        bindMemberFunctions(this);\n        if (callback && !this.isInitialized && !options.isClone) {\n            if (!this.options.initImmediate) {\n                this.init(options, callback);\n                return this;\n            }\n            setTimeout(()=>{\n                this.init(options, callback);\n            }, 0);\n        }\n    }\n    init() {\n        var _this = this;\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        let callback = arguments.length > 1 ? arguments[1] : undefined;\n        this.isInitializing = true;\n        if (typeof options === \"function\") {\n            callback = options;\n            options = {};\n        }\n        if (!options.defaultNS && options.defaultNS !== false && options.ns) {\n            if (isString(options.ns)) {\n                options.defaultNS = options.ns;\n            } else if (options.ns.indexOf(\"translation\") < 0) {\n                options.defaultNS = options.ns[0];\n            }\n        }\n        const defOpts = get();\n        this.options = {\n            ...defOpts,\n            ...this.options,\n            ...transformOptions(options)\n        };\n        if (this.options.compatibilityAPI !== \"v1\") {\n            this.options.interpolation = {\n                ...defOpts.interpolation,\n                ...this.options.interpolation\n            };\n        }\n        if (options.keySeparator !== undefined) {\n            this.options.userDefinedKeySeparator = options.keySeparator;\n        }\n        if (options.nsSeparator !== undefined) {\n            this.options.userDefinedNsSeparator = options.nsSeparator;\n        }\n        const createClassOnDemand = (ClassOrObject)=>{\n            if (!ClassOrObject) return null;\n            if (typeof ClassOrObject === \"function\") return new ClassOrObject();\n            return ClassOrObject;\n        };\n        if (!this.options.isClone) {\n            if (this.modules.logger) {\n                baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n            } else {\n                baseLogger.init(null, this.options);\n            }\n            let formatter;\n            if (this.modules.formatter) {\n                formatter = this.modules.formatter;\n            } else if (typeof Intl !== \"undefined\") {\n                formatter = Formatter;\n            }\n            const lu = new LanguageUtil(this.options);\n            this.store = new ResourceStore(this.options.resources, this.options);\n            const s = this.services;\n            s.logger = baseLogger;\n            s.resourceStore = this.store;\n            s.languageUtils = lu;\n            s.pluralResolver = new PluralResolver(lu, {\n                prepend: this.options.pluralSeparator,\n                compatibilityJSON: this.options.compatibilityJSON,\n                simplifyPluralSuffix: this.options.simplifyPluralSuffix\n            });\n            if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n                s.formatter = createClassOnDemand(formatter);\n                s.formatter.init(s, this.options);\n                this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n            }\n            s.interpolator = new Interpolator(this.options);\n            s.utils = {\n                hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n            };\n            s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n            s.backendConnector.on(\"*\", function(event) {\n                for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n                    args[_key - 1] = arguments[_key];\n                }\n                _this.emit(event, ...args);\n            });\n            if (this.modules.languageDetector) {\n                s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n                if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n            }\n            if (this.modules.i18nFormat) {\n                s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n                if (s.i18nFormat.init) s.i18nFormat.init(this);\n            }\n            this.translator = new Translator(this.services, this.options);\n            this.translator.on(\"*\", function(event) {\n                for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n                    args[_key2 - 1] = arguments[_key2];\n                }\n                _this.emit(event, ...args);\n            });\n            this.modules.external.forEach((m)=>{\n                if (m.init) m.init(this);\n            });\n        }\n        this.format = this.options.interpolation.format;\n        if (!callback) callback = noop;\n        if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n            const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n            if (codes.length > 0 && codes[0] !== \"dev\") this.options.lng = codes[0];\n        }\n        if (!this.services.languageDetector && !this.options.lng) {\n            this.logger.warn(\"init: no languageDetector is used and no lng is defined\");\n        }\n        const storeApi = [\n            \"getResource\",\n            \"hasResourceBundle\",\n            \"getResourceBundle\",\n            \"getDataByLanguage\"\n        ];\n        storeApi.forEach((fcName)=>{\n            this[fcName] = function() {\n                return _this.store[fcName](...arguments);\n            };\n        });\n        const storeApiChained = [\n            \"addResource\",\n            \"addResources\",\n            \"addResourceBundle\",\n            \"removeResourceBundle\"\n        ];\n        storeApiChained.forEach((fcName)=>{\n            this[fcName] = function() {\n                _this.store[fcName](...arguments);\n                return _this;\n            };\n        });\n        const deferred = defer();\n        const load = ()=>{\n            const finish = (err, t)=>{\n                this.isInitializing = false;\n                if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn(\"init: i18next is already initialized. You should call init just once!\");\n                this.isInitialized = true;\n                if (!this.options.isClone) this.logger.log(\"initialized\", this.options);\n                this.emit(\"initialized\", this.options);\n                deferred.resolve(t);\n                callback(err, t);\n            };\n            if (this.languages && this.options.compatibilityAPI !== \"v1\" && !this.isInitialized) return finish(null, this.t.bind(this));\n            this.changeLanguage(this.options.lng, finish);\n        };\n        if (this.options.resources || !this.options.initImmediate) {\n            load();\n        } else {\n            setTimeout(load, 0);\n        }\n        return deferred;\n    }\n    loadResources(language) {\n        let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n        let usedCallback = callback;\n        const usedLng = isString(language) ? language : this.language;\n        if (typeof language === \"function\") usedCallback = language;\n        if (!this.options.resources || this.options.partialBundledLanguages) {\n            if (usedLng && usedLng.toLowerCase() === \"cimode\" && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n            const toLoad = [];\n            const append = (lng)=>{\n                if (!lng) return;\n                if (lng === \"cimode\") return;\n                const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n                lngs.forEach((l)=>{\n                    if (l === \"cimode\") return;\n                    if (toLoad.indexOf(l) < 0) toLoad.push(l);\n                });\n            };\n            if (!usedLng) {\n                const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n                fallbacks.forEach((l)=>append(l));\n            } else {\n                append(usedLng);\n            }\n            if (this.options.preload) {\n                this.options.preload.forEach((l)=>append(l));\n            }\n            this.services.backendConnector.load(toLoad, this.options.ns, (e)=>{\n                if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n                usedCallback(e);\n            });\n        } else {\n            usedCallback(null);\n        }\n    }\n    reloadResources(lngs, ns, callback) {\n        const deferred = defer();\n        if (typeof lngs === \"function\") {\n            callback = lngs;\n            lngs = undefined;\n        }\n        if (typeof ns === \"function\") {\n            callback = ns;\n            ns = undefined;\n        }\n        if (!lngs) lngs = this.languages;\n        if (!ns) ns = this.options.ns;\n        if (!callback) callback = noop;\n        this.services.backendConnector.reload(lngs, ns, (err)=>{\n            deferred.resolve();\n            callback(err);\n        });\n        return deferred;\n    }\n    use(module) {\n        if (!module) throw new Error(\"You are passing an undefined module! Please check the object you are passing to i18next.use()\");\n        if (!module.type) throw new Error(\"You are passing a wrong module! Please check the object you are passing to i18next.use()\");\n        if (module.type === \"backend\") {\n            this.modules.backend = module;\n        }\n        if (module.type === \"logger\" || module.log && module.warn && module.error) {\n            this.modules.logger = module;\n        }\n        if (module.type === \"languageDetector\") {\n            this.modules.languageDetector = module;\n        }\n        if (module.type === \"i18nFormat\") {\n            this.modules.i18nFormat = module;\n        }\n        if (module.type === \"postProcessor\") {\n            postProcessor.addPostProcessor(module);\n        }\n        if (module.type === \"formatter\") {\n            this.modules.formatter = module;\n        }\n        if (module.type === \"3rdParty\") {\n            this.modules.external.push(module);\n        }\n        return this;\n    }\n    setResolvedLanguage(l) {\n        if (!l || !this.languages) return;\n        if ([\n            \"cimode\",\n            \"dev\"\n        ].indexOf(l) > -1) return;\n        for(let li = 0; li < this.languages.length; li++){\n            const lngInLngs = this.languages[li];\n            if ([\n                \"cimode\",\n                \"dev\"\n            ].indexOf(lngInLngs) > -1) continue;\n            if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n                this.resolvedLanguage = lngInLngs;\n                break;\n            }\n        }\n    }\n    changeLanguage(lng, callback) {\n        var _this2 = this;\n        this.isLanguageChangingTo = lng;\n        const deferred = defer();\n        this.emit(\"languageChanging\", lng);\n        const setLngProps = (l)=>{\n            this.language = l;\n            this.languages = this.services.languageUtils.toResolveHierarchy(l);\n            this.resolvedLanguage = undefined;\n            this.setResolvedLanguage(l);\n        };\n        const done = (err, l)=>{\n            if (l) {\n                setLngProps(l);\n                this.translator.changeLanguage(l);\n                this.isLanguageChangingTo = undefined;\n                this.emit(\"languageChanged\", l);\n                this.logger.log(\"languageChanged\", l);\n            } else {\n                this.isLanguageChangingTo = undefined;\n            }\n            deferred.resolve(function() {\n                return _this2.t(...arguments);\n            });\n            if (callback) callback(err, function() {\n                return _this2.t(...arguments);\n            });\n        };\n        const setLng = (lngs)=>{\n            if (!lng && !lngs && this.services.languageDetector) lngs = [];\n            const l = isString(lngs) ? lngs : this.services.languageUtils.getBestMatchFromCodes(lngs);\n            if (l) {\n                if (!this.language) {\n                    setLngProps(l);\n                }\n                if (!this.translator.language) this.translator.changeLanguage(l);\n                if (this.services.languageDetector && this.services.languageDetector.cacheUserLanguage) this.services.languageDetector.cacheUserLanguage(l);\n            }\n            this.loadResources(l, (err)=>{\n                done(err, l);\n            });\n        };\n        if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n            setLng(this.services.languageDetector.detect());\n        } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n            if (this.services.languageDetector.detect.length === 0) {\n                this.services.languageDetector.detect().then(setLng);\n            } else {\n                this.services.languageDetector.detect(setLng);\n            }\n        } else {\n            setLng(lng);\n        }\n        return deferred;\n    }\n    getFixedT(lng, ns, keyPrefix) {\n        var _this3 = this;\n        const fixedT = function(key, opts) {\n            let options;\n            if (typeof opts !== \"object\") {\n                for(var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++){\n                    rest[_key3 - 2] = arguments[_key3];\n                }\n                options = _this3.options.overloadTranslationOptionHandler([\n                    key,\n                    opts\n                ].concat(rest));\n            } else {\n                options = {\n                    ...opts\n                };\n            }\n            options.lng = options.lng || fixedT.lng;\n            options.lngs = options.lngs || fixedT.lngs;\n            options.ns = options.ns || fixedT.ns;\n            if (options.keyPrefix !== \"\") options.keyPrefix = options.keyPrefix || keyPrefix || fixedT.keyPrefix;\n            const keySeparator = _this3.options.keySeparator || \".\";\n            let resultKey;\n            if (options.keyPrefix && Array.isArray(key)) {\n                resultKey = key.map((k)=>`${options.keyPrefix}${keySeparator}${k}`);\n            } else {\n                resultKey = options.keyPrefix ? `${options.keyPrefix}${keySeparator}${key}` : key;\n            }\n            return _this3.t(resultKey, options);\n        };\n        if (isString(lng)) {\n            fixedT.lng = lng;\n        } else {\n            fixedT.lngs = lng;\n        }\n        fixedT.ns = ns;\n        fixedT.keyPrefix = keyPrefix;\n        return fixedT;\n    }\n    t() {\n        return this.translator && this.translator.translate(...arguments);\n    }\n    exists() {\n        return this.translator && this.translator.exists(...arguments);\n    }\n    setDefaultNamespace(ns) {\n        this.options.defaultNS = ns;\n    }\n    hasLoadedNamespace(ns) {\n        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        if (!this.isInitialized) {\n            this.logger.warn(\"hasLoadedNamespace: i18next was not initialized\", this.languages);\n            return false;\n        }\n        if (!this.languages || !this.languages.length) {\n            this.logger.warn(\"hasLoadedNamespace: i18n.languages were undefined or empty\", this.languages);\n            return false;\n        }\n        const lng = options.lng || this.resolvedLanguage || this.languages[0];\n        const fallbackLng = this.options ? this.options.fallbackLng : false;\n        const lastLng = this.languages[this.languages.length - 1];\n        if (lng.toLowerCase() === \"cimode\") return true;\n        const loadNotPending = (l, n)=>{\n            const loadState = this.services.backendConnector.state[`${l}|${n}`];\n            return loadState === -1 || loadState === 0 || loadState === 2;\n        };\n        if (options.precheck) {\n            const preResult = options.precheck(this, loadNotPending);\n            if (preResult !== undefined) return preResult;\n        }\n        if (this.hasResourceBundle(lng, ns)) return true;\n        if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n        if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n        return false;\n    }\n    loadNamespaces(ns, callback) {\n        const deferred = defer();\n        if (!this.options.ns) {\n            if (callback) callback();\n            return Promise.resolve();\n        }\n        if (isString(ns)) ns = [\n            ns\n        ];\n        ns.forEach((n)=>{\n            if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n        });\n        this.loadResources((err)=>{\n            deferred.resolve();\n            if (callback) callback(err);\n        });\n        return deferred;\n    }\n    loadLanguages(lngs, callback) {\n        const deferred = defer();\n        if (isString(lngs)) lngs = [\n            lngs\n        ];\n        const preloaded = this.options.preload || [];\n        const newLngs = lngs.filter((lng)=>preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n        if (!newLngs.length) {\n            if (callback) callback();\n            return Promise.resolve();\n        }\n        this.options.preload = preloaded.concat(newLngs);\n        this.loadResources((err)=>{\n            deferred.resolve();\n            if (callback) callback(err);\n        });\n        return deferred;\n    }\n    dir(lng) {\n        if (!lng) lng = this.resolvedLanguage || (this.languages && this.languages.length > 0 ? this.languages[0] : this.language);\n        if (!lng) return \"rtl\";\n        const rtlLngs = [\n            \"ar\",\n            \"shu\",\n            \"sqr\",\n            \"ssh\",\n            \"xaa\",\n            \"yhd\",\n            \"yud\",\n            \"aao\",\n            \"abh\",\n            \"abv\",\n            \"acm\",\n            \"acq\",\n            \"acw\",\n            \"acx\",\n            \"acy\",\n            \"adf\",\n            \"ads\",\n            \"aeb\",\n            \"aec\",\n            \"afb\",\n            \"ajp\",\n            \"apc\",\n            \"apd\",\n            \"arb\",\n            \"arq\",\n            \"ars\",\n            \"ary\",\n            \"arz\",\n            \"auz\",\n            \"avl\",\n            \"ayh\",\n            \"ayl\",\n            \"ayn\",\n            \"ayp\",\n            \"bbz\",\n            \"pga\",\n            \"he\",\n            \"iw\",\n            \"ps\",\n            \"pbt\",\n            \"pbu\",\n            \"pst\",\n            \"prp\",\n            \"prd\",\n            \"ug\",\n            \"ur\",\n            \"ydd\",\n            \"yds\",\n            \"yih\",\n            \"ji\",\n            \"yi\",\n            \"hbo\",\n            \"men\",\n            \"xmn\",\n            \"fa\",\n            \"jpr\",\n            \"peo\",\n            \"pes\",\n            \"prs\",\n            \"dv\",\n            \"sam\",\n            \"ckb\"\n        ];\n        const languageUtils = this.services && this.services.languageUtils || new LanguageUtil(get());\n        return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf(\"-arab\") > 1 ? \"rtl\" : \"ltr\";\n    }\n    static createInstance() {\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        let callback = arguments.length > 1 ? arguments[1] : undefined;\n        return new I18n(options, callback);\n    }\n    cloneInstance() {\n        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n        const forkResourceStore = options.forkResourceStore;\n        if (forkResourceStore) delete options.forkResourceStore;\n        const mergedOptions = {\n            ...this.options,\n            ...options,\n            ...{\n                isClone: true\n            }\n        };\n        const clone = new I18n(mergedOptions);\n        if (options.debug !== undefined || options.prefix !== undefined) {\n            clone.logger = clone.logger.clone(options);\n        }\n        const membersToCopy = [\n            \"store\",\n            \"services\",\n            \"language\"\n        ];\n        membersToCopy.forEach((m)=>{\n            clone[m] = this[m];\n        });\n        clone.services = {\n            ...this.services\n        };\n        clone.services.utils = {\n            hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n        };\n        if (forkResourceStore) {\n            clone.store = new ResourceStore(this.store.data, mergedOptions);\n            clone.services.resourceStore = clone.store;\n        }\n        clone.translator = new Translator(clone.services, mergedOptions);\n        clone.translator.on(\"*\", function(event) {\n            for(var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++){\n                args[_key4 - 1] = arguments[_key4];\n            }\n            clone.emit(event, ...args);\n        });\n        clone.init(mergedOptions, callback);\n        clone.translator.options = mergedOptions;\n        clone.translator.backendConnector.services.utils = {\n            hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n        };\n        return clone;\n    }\n    toJSON() {\n        return {\n            options: this.options,\n            store: this.store,\n            language: this.language,\n            languages: this.languages,\n            resolvedLanguage: this.resolvedLanguage\n        };\n    }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next/dist/esm/i18next.js\n");

/***/ })

};
;